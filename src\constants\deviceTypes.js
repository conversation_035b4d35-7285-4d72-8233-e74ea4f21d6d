/**
 * 设备类型常量定义
 * 统一管理所有设备类型相关的常量
 */

// 设备类型常量
export const DEVICE_TYPE_GATEWAY = 1;      // 网关
export const DEVICE_TYPE_DOCK = 2;         // 机场
export const DEVICE_TYPE_DRONE = 3;        // 无人机
export const DEVICE_TYPE_REMOTE = 4;       // 遥控器

// 域类型常量（用于日志接口）
export const DOMAIN_TYPE_DRONE = 0;        // 无人机域
export const DOMAIN_TYPE_DOCK = 3;         // 机场域

// 设备类型映射
export const DEVICE_TYPE_MAP = {
  [DEVICE_TYPE_GATEWAY]: '网关',
  [DEVICE_TYPE_DOCK]: '机场',
  [DEVICE_TYPE_DRONE]: '无人机',
  [DEVICE_TYPE_REMOTE]: '遥控器'
};

// 设备类型标签样式映射
export const DEVICE_TYPE_TAG_MAP = {
  [DEVICE_TYPE_GATEWAY]: 'primary',
  [DEVICE_TYPE_DOCK]: 'success',
  [DEVICE_TYPE_DRONE]: 'warning',
  [DEVICE_TYPE_REMOTE]: 'info'
};

// 在线状态常量
export const ONLINE_STATUS_OFFLINE = -1;   // 离线
export const ONLINE_STATUS_ONLINE = 0;     // 在线

// 绑定状态常量
export const BIND_STATUS_UNBOUND = false;  // 未绑定
export const BIND_STATUS_BOUND = true;     // 已绑定

// 工具函数：获取设备类型名称
export const getDeviceTypeName = (type) => {
  return DEVICE_TYPE_MAP[type] || '未知';
};

// 工具函数：获取设备类型标签样式
export const getDeviceTypeTag = (type) => {
  return DEVICE_TYPE_TAG_MAP[type] || 'default';
};

// 工具函数：判断设备是否在线
export const isDeviceOnline = (modeCode) => {
  return modeCode >= ONLINE_STATUS_ONLINE;
};

// 工具函数：根据设备类型获取域类型
export const getDomainByDeviceType = (deviceType) => {
  return deviceType === DEVICE_TYPE_DOCK ? DOMAIN_TYPE_DOCK : DOMAIN_TYPE_DRONE;
};

// 默认导出
export default {
  DEVICE_TYPE_GATEWAY,
  DEVICE_TYPE_DOCK,
  DEVICE_TYPE_DRONE,
  DEVICE_TYPE_REMOTE,
  DOMAIN_TYPE_DRONE,
  DOMAIN_TYPE_DOCK,
  DEVICE_TYPE_MAP,
  DEVICE_TYPE_TAG_MAP,
  ONLINE_STATUS_OFFLINE,
  ONLINE_STATUS_ONLINE,
  BIND_STATUS_UNBOUND,
  BIND_STATUS_BOUND,
  getDeviceTypeName,
  getDeviceTypeTag,
  isDeviceOnline,
  getDomainByDeviceType
};
