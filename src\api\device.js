import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/manage/api/v1/devices/getDevicesByParams',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getDevice(device_sn) {
  return request({
    url: '/device/' + parseStrEmpty(device_sn),
    method: 'get'
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/device',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateDevice(data) {
  return request({
    url: '/device',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delDevice(device_sn) {
  return request({
    url: '/device/' + device_sn,
    method: 'delete'
  })
}

// 修改设备绑定状态
export function changeDeviceBindStatus(device_sn, bound_status) {
  const data = {
    device_sn,
    bound_status
  }
  return request({
    url: '/device/changeBoundStatus',
    method: 'put',
    data: data
  })
}

// 获取设备类型字典
export function getDeviceTypes() {
  return request({
    url: '/device/types',
    method: 'get'
  })
}

// 获取工作空间列表
export function getWorkspaceList() {
  return request({
    url: '/device/workspaces',
    method: 'get'
  })
}

// 获取机场设备信息
export function getDockInfo(dockSn) {
  return request({
    url: '/manage/api/v1/devices/getDockInfo',
    method: 'get',
    params: {
      dockSn: dockSn
    }
  })
}

// 获取无人机设备信息
export function getDroneInfo(droneSn) {
  return request({
    url: '/manage/api/v1/devices/getDroneInfo',
    method: 'get',
    params: {
      droneSn: droneSn
    }
  })
}

// 获取设备日志列表
export function getDeviceLogs(deviceSn, domainList) {
  return request({
    url: '/device/logs/get',
    method: 'get',
    params: {
      deviceSn: deviceSn,
      domain_list: domainList
    }
  })
}
