<template>
    <div class="wayline-list-container">
        <!-- 搜索条件区域 -->
        <div class="search-container">
            <el-card shadow="never" class="search-card">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                    label-width="68px">
                    <el-form-item label="航线名称" prop="name">
                        <el-input v-model="queryParams.name" placeholder="请输入航线名称" clearable
                            @keyup.enter.native="handleQuery" />
                    </el-form-item>
                    <el-form-item label="创建时间" prop="dateRange">
                        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd"
                            type="daterange" range-separator="-" start-placeholder="开始日期"
                            end-placeholder="结束日期"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="收藏状态" prop="favorited">
                        <el-select v-model="queryParams.favorited" placeholder="请选择收藏状态" clearable>
                            <el-option label="全部" value=""></el-option>
                            <el-option label="已收藏" value="true"></el-option>
                            <el-option label="未收藏" value="false"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <el-button type="success" icon="el-icon-plus" size="mini" @click="showCreateDialog">新增航线</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <!-- 航线卡片列表区域 -->
        <div class="wayline-cards-container">
            <el-row :gutter="20" v-loading="loading">
                <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="wayline in waylineList" :key="wayline.id">
                    <el-card :body-style="{ padding: '0px' }" shadow="hover" class="wayline-card"
                        @click.native="handleCardClick(wayline)">
                        <!-- 航线封面图片 -->
                        <div class="wayline-image-container">
                            <img :src="getWaylineImageUrl(wayline.cover)" :alt="wayline.name" class="wayline-image"
                                @error="handleImageError" />

                            <!-- 图片加载指示器 -->
                            <div v-if="isImageLoading(wayline.cover)" class="image-loading">
                                <i class="el-icon-loading"></i>
                                <span>加载中...</span>
                            </div>

                            <!-- 收藏图标 -->
                            <div class="favorite-icon" @click.stop="toggleFavorite(wayline)">
                                <i :class="wayline.favorited ? 'el-icon-star-on favorite-active' : 'el-icon-star-off'"></i>
                            </div>

                            <!-- 删除图标 -->
                            <div class="delete-icon" @click.stop="confirmDelete(wayline)">
                                <i class="el-icon-delete"></i>
                            </div>
                        </div>

                        <!-- 航线信息 -->
                        <div class="wayline-info">
                            <div class="wayline-title" :title="wayline.name">{{ wayline.name }}</div>
                            <div class="wayline-meta">
                                <div class="meta-item">
                                    <i class="el-icon-s-grid"></i>
                                    <span>{{ getTemplateTypeText(wayline.template_types) }}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="el-icon-user"></i>
                                    <span>{{ wayline.username || '未知用户' }}</span>
                                </div>
                            </div>
                            <div class="wayline-author">
                                <i class="el-icon-time"></i>
                                <span>{{ formatDate(wayline.create_time) }}</span>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 空状态 -->
            <div v-if="!loading && waylineList.length === 0" class="empty-state">
                <el-empty description="暂无航线数据">
                    <el-button type="primary" @click="showCreateDialog">新增航线</el-button>
                </el-empty>
            </div>
        </div>

        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 创建航线模式选择对话框 -->
        <el-dialog title="选择航线模式" :visible.sync="createDialogVisible" width="800px" :close-on-click-modal="false">
            <div class="wayline-mode-container">
                <div class="wayline-mode-grid">
                    <div
                        v-for="mode in waylineModes"
                        :key="mode.value"
                        class="wayline-mode-item"
                        @click="selectWaylineMode(mode)"
                    >
                        <div class="mode-image-container">
                            <img :src="mode.image" :alt="mode.label" class="mode-image" />
                        </div>
                        <div class="mode-label">{{ mode.label }}</div>
                    </div>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="createDialogVisible = false">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getWaylines, delWayline, favoriteWayline } from "@/api/flightAirline/airline";
import request from "@/utils/request";

export default {
    name: "WaylineList",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 航线表格数据
            waylineList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 日期范围
            dateRange: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 12,
                name: null,
                favorited: null,
                beginTime: null,
                endTime: null
            },
            // 图片缓存 - 存储blob URL
            imageCache: new Map(),
            // 图片加载状态
            imageLoadingStatus: new Map(),
            // 创建航线对话框显示状态
            createDialogVisible: false,
            // 航线模式选项
            waylineModes: [
                {
                    value: 'waypoint',
                    label: '航点飞行',
                    image: require('@/assets/images/wayline/hx_waypoint.png')
                },
                {
                    value: 'mapping2d',
                    label: '建图航拍',
                    image: require('@/assets/images/wayline/hx_mapping2d.png')
                },
                {
                    value: 'mapping3d',
                    label: '倾斜摄影',
                    image: require('@/assets/images/wayline/hx_mapping3d.png')
                },
                {
                    value: 'mappingStrip',
                    label: '航带飞行',
                    image: require('@/assets/images/wayline/hx_mappingStrip.png')
                }
            ]
        };
    },
    created() {
        this.getList();
    },

    beforeDestroy() {
        // 清理blob URLs，防止内存泄漏
        this.imageCache.forEach(url => {
            if (url && url.startsWith('blob:')) {
                URL.revokeObjectURL(url);
            }
        });
        this.imageCache.clear();
        this.imageLoadingStatus.clear();
    },
    methods: {
        /** 查询航线列表 */
        getList() {
            this.loading = true;

            // 处理日期范围参数
            const params = { ...this.queryParams };
            if (this.dateRange && this.dateRange.length === 2) {
                params.beginTime = this.dateRange[0];
                params.endTime = this.dateRange[1];
            } else {
                params.beginTime = null;
                params.endTime = null;
            }

            getWaylines(params).then(response => {
                this.waylineList = response.rows || response.data || [];
                this.total = response.total || 0;
                this.loading = false;

                // 预加载图片
                this.preloadImages();
            }).catch(() => {
                this.loading = false;
            });
        },

        /** 预加载图片 */
        preloadImages() {
            this.waylineList.forEach(wayline => {
                if (wayline.cover && !this.imageCache.has(wayline.cover)) {
                    // 延迟加载，避免同时发起太多请求
                    setTimeout(() => {
                        this.downloadImage(wayline.cover);
                    }, Math.random() * 1000);
                }
            });
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },

        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            if (this.$refs.queryForm) {
                this.$refs.queryForm.resetFields();
            }
            this.handleQuery();
        },

        /** 获取航线封面图片URL */
        getWaylineImageUrl(cover) {
            if (!cover) {
                return require('@/assets/images/default-wayline.svg');
            }

            // 检查缓存
            if (this.imageCache.has(cover)) {
                return this.imageCache.get(cover);
            }

            // 检查是否正在加载
            if (this.imageLoadingStatus.get(cover) === 'loading') {
                return require('@/assets/images/default-wayline.svg');
            }

            // 开始下载图片
            this.downloadImage(cover);

            // 返回默认图片，等待下载完成后更新
            return require('@/assets/images/default-wayline.svg');
        },

        /** 下载图片 */
        async downloadImage(cover) {
            try {
                console.log('开始下载图片:', cover);
                this.imageLoadingStatus.set(cover, 'loading');

                // 使用系统request方法下载图片
                const response = await request({
                    url: `/wayline/resource/${cover}`,
                    method: 'get',
                    responseType: 'blob'
                });

                // 创建blob URL
                const blob = new Blob([response], { type: 'image/png' });
                const imageUrl = URL.createObjectURL(blob);

                // 缓存图片URL
                this.imageCache.set(cover, imageUrl);
                this.imageLoadingStatus.set(cover, 'loaded');

                console.log('图片下载成功:', cover, imageUrl);

                // 触发重新渲染
                this.$forceUpdate();

            } catch (error) {
                console.error('图片下载失败:', cover, error);
                this.imageLoadingStatus.set(cover, 'error');

                // 缓存默认图片
                this.imageCache.set(cover, require('@/assets/images/default-wayline.svg'));

                // 触发重新渲染
                this.$forceUpdate();
            }
        },

        /** 检查图片是否正在加载 */
        isImageLoading(cover) {
            return this.imageLoadingStatus.get(cover) === 'loading';
        },

        /** 图片加载错误处理 */
        handleImageError(event) {
            console.error('图片加载失败:', event.target.src);
            console.log('切换到默认图片');
            event.target.src = require('@/assets/images/default-wayline.svg');
            // 防止无限循环
            event.target.onerror = null;
        },

        /** 格式化日期 */
        formatDate(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        /** 获取模板类型文本 */
        getTemplateTypeText(templateType) {
            const typeMap = {
                '0': '航点飞行',
                '1': '建图航拍',
                '2': '倾斜摄影',
                '3': '航带飞行'
            };
            return typeMap[templateType] || '未知类型';
        },

        /** 切换收藏状态 */
        async toggleFavorite(wayline) {
            const newFavoriteStatus = !wayline.favorited;

            try {
                await favoriteWayline(wayline.id, newFavoriteStatus);
                wayline.favorited = newFavoriteStatus;
                this.$message.success(newFavoriteStatus ? '已收藏' : '已取消收藏');
            } catch (error) {
                console.error('更新收藏状态失败:', error);
                this.$message.error('操作失败，请稍后重试');
            }
        },

        /** 卡片点击事件 */
        handleCardClick(wayline) {
            // 使用路径参数方式跳转，类似字典管理
            this.$router.push(`/flightAirline/edit/${wayline.id}`)
        },

        /** 创建航线 */
        handleCreate() {
            this.$router.push('/flightAirline/create');
        },

        /** 显示创建航线对话框 */
        showCreateDialog() {
            this.createDialogVisible = true;
        },

        /** 选择航线模式 */
        selectWaylineMode(mode) {
            this.createDialogVisible = false;
            // 跳转到航线创建页面，传递模式参数
            this.$router.push({
                path: '/flightAirline/create',
                query: { mode: mode.value }
            });
        },

        /** 确认删除航线 */
        confirmDelete(wayline) {
            this.$confirm(`确定要删除航线"${wayline.name}"吗？删除后无法恢复！`, '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                confirmButtonClass: 'el-button--danger'
            }).then(() => {
                this.deleteWayline(wayline);
            }).catch(() => {
                // 用户取消删除
            });
        },

        /** 删除航线 */
        async deleteWayline(wayline) {
            try {
                await delWayline(wayline.id);
                this.$message.success('删除成功');
                // 重新加载列表
                this.getList();
            } catch (error) {
                console.error('删除航线失败:', error);
                this.$message.error('删除失败，请稍后重试');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.wayline-list-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 84px);

    .search-container {
        margin-bottom: 20px;

        .search-card {
            border-radius: 8px;

            ::v-deep .el-card__body {
                padding: 20px;
            }
        }
    }

    .wayline-cards-container {
        margin-bottom: 20px;

        .wayline-card {
            margin-bottom: 20px;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            height: 320px;

            &:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            .wayline-image-container {
                position: relative;
                height: 200px;
                overflow: hidden;

                .wayline-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: transform 0.3s ease;
                }

                &:hover .wayline-image {
                    transform: scale(1.05);
                }

                .image-loading {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(255, 255, 255, 0.9);
                    padding: 10px 15px;
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 12px;
                    color: #666;
                    backdrop-filter: blur(4px);

                    i {
                        font-size: 14px;
                        animation: rotate 1s linear infinite;
                    }

                    @keyframes rotate {
                        from {
                            transform: rotate(0deg);
                        }

                        to {
                            transform: rotate(360deg);
                        }
                    }
                }

                .favorite-icon {
                    position: absolute;
                    top: 12px;
                    right: 12px;
                    width: 32px;
                    height: 32px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(4px);

                    &:hover {
                        background: rgba(255, 255, 255, 1);
                        transform: scale(1.1);
                    }

                    i {
                        font-size: 16px;
                        color: #ccc;
                        transition: color 0.3s ease;

                        &.favorite-active {
                            color: #f56c6c;
                        }
                    }
                }

                .delete-icon {
                    position: absolute;
                    top: 12px;
                    right: 52px;
                    width: 32px;
                    height: 32px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(4px);

                    &:hover {
                        background: rgba(255, 255, 255, 1);
                        transform: scale(1.1);
                    }

                    i {
                        font-size: 16px;
                        color: #ccc;
                        transition: color 0.3s ease;

                        &:hover {
                            color: #f56c6c;
                        }
                    }

                    &:hover i {
                        color: #f56c6c;
                    }
                }
            }

            .wayline-info {
                padding: 16px;
                height: 120px;
                display: flex;
                flex-direction: column;

                .wayline-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 8px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    line-height: 1.4;
                }

                .wayline-meta {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: nowrap;
                    justify-content: space-between;
                    gap: 4px;
                    margin-bottom: 8px;
                    .meta-item {
                        display: flex;
                        align-items: center;
                        font-size: 12px;
                        color: #909399;
                        i {
                            margin-right: 4px;
                            font-size: 14px;
                            min-width: 14px;
                        }
                    }
                }

                .wayline-author {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    font-size: 13px;
                    color: #606266;
                    margin-top: 4px;

                    i {
                        margin-right: 4px;
                        font-size: 14px;
                    }
                }
            }
        }

        .empty-state {
            text-align: center;
            padding: 60px 0;
            background: white;
            border-radius: 12px;
            margin-top: 20px;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 10px;

        .search-container {
            .search-card ::v-deep .el-card__body {
                padding: 15px;
            }

            .el-form--inline .el-form-item {
                display: block;
                margin-right: 0;
                margin-bottom: 15px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .wayline-cards-container {
            .wayline-card {
                height: auto;

                .wayline-info {
                    height: auto;
                    min-height: 100px;
                }
            }
        }
    }
}

// 分页组件样式调整
::v-deep .pagination-container {
    background: white;
    border-radius: 12px;
    margin-top: 20px;
}

// 创建航线模式选择对话框样式
.wayline-mode-container {
    padding: 20px 0;

    .wayline-mode-grid {
        display: flex;
        justify-content: space-around;
        gap: 20px;
        flex-wrap: wrap;

        .wayline-mode-item {
            flex: 1;
            min-width: 150px;
            max-width: 180px;
            text-align: center;
            cursor: pointer;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e4e7ed;
            transition: all 0.3s ease;
            background: #fff;

            &:hover {
                border-color: #409eff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
                transform: translateY(-2px);
            }

            .mode-image-container {
                margin-bottom: 15px;

                .mode-image {
                    width: 80px;
                    height: 80px;
                    object-fit: contain;
                    border-radius: 8px;
                }
            }

            .mode-label {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                line-height: 1.4;
            }
        }
    }
}

// 对话框样式调整
::v-deep .el-dialog {
    .el-dialog__header {
        padding: 20px 20px 10px;
        border-bottom: 1px solid #ebeef5;

        .el-dialog__title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
    }

    .el-dialog__body {
        padding: 0 20px;
    }

    .el-dialog__footer {
        padding: 15px 20px 20px;
        border-top: 1px solid #ebeef5;
    }
}
</style>