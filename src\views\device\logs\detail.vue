<template>
  <div class="app-container">
    <!-- 设备信息头部 -->
    <el-card class="device-info-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span class="device-title">设备日志 - {{ deviceInfo.nickname || deviceInfo.device_name }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">设备序列号：</span>
            <span class="info-value">{{ deviceInfo.device_sn }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">设备类型：</span>
            <el-tag :type="getDeviceTypeTag(deviceInfo.type)">{{ getDeviceTypeName(deviceInfo.type) }}</el-tag>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">在线状态：</span>
            <el-tag :type="deviceInfo.mode_code >= 0 ? 'success' : 'danger'">
              {{ deviceInfo.mode_code >= 0 ? '在线' : '离线' }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">最后登录：</span>
            <span class="info-value">{{ deviceInfo.login_time || '-' }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 日志列表 -->
    <el-card class="logs-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span>日志文件列表 ({{ logsList.length }}个文件)</span>
        <div style="float: right;">
          <el-button type="text" @click="refreshLogs" :loading="loading">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>
      
      <el-table v-loading="loading" :data="logsList" stripe style="width: 100%">
        <el-table-column label="启动索引" align="center" prop="boot_index" min-width="100" />
        <el-table-column label="开始时间" align="center" min-width="160">
          <template slot-scope="scope">
            <span>{{ formatTimestamp(scope.row.start_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" min-width="160">
          <template slot-scope="scope">
            <span>{{ formatTimestamp(scope.row.end_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="持续时间" align="center" min-width="100">
          <template slot-scope="scope">
            <span>{{ formatDuration(scope.row.start_time, scope.row.end_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="文件大小" align="center" min-width="100">
          <template slot-scope="scope">
            <span>{{ formatFileSize(scope.row.size) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleViewLog(scope.row)"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleDownloadLog(scope.row)"
            >下载</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && logsList.length === 0" class="empty-state">
        <i class="el-icon-document"></i>
        <p>暂无日志文件</p>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDeviceLogs } from "@/api/device";
import { SUCCESS_CODE } from "@/constants/responseCode";
import { getDeviceTypeName, getDeviceTypeTag } from "@/constants/deviceTypes";

export default {
  name: "DeviceLogsDetail",
  data() {
    return {
      loading: false,
      deviceInfo: {},
      logsList: [],
      deviceSn: '',
      domainList: 0 // 0=无人机, 3=机场
    };
  },
  created() {
    this.initData();
  },
  methods: {
    // 初始化数据
    initData() {
      // 从路由参数获取设备信息
      this.deviceSn = this.$route.query.deviceSn;
      this.domainList = this.$route.query.domainList || 0;
      
      if (this.$route.query.deviceInfo) {
        try {
          this.deviceInfo = JSON.parse(decodeURIComponent(this.$route.query.deviceInfo));
        } catch (e) {
          console.error('解析设备信息失败:', e);
          this.deviceInfo = { device_sn: this.deviceSn };
        }
      } else {
        this.deviceInfo = { device_sn: this.deviceSn };
      }
      
      this.getLogsList();
    },
    
    // 获取日志列表
    getLogsList() {
      if (!this.deviceSn) {
        this.$modal.msgError("设备序列号不能为空");
        return;
      }
      
      this.loading = true;
      getDeviceLogs(this.deviceSn, this.domainList).then(response => {
        if (response.code === SUCCESS_CODE && response.data && response.data.files) {
          // 处理返回的日志数据
          const fileData = response.data.files[0];
          if (fileData && fileData.list) {
            this.logsList = fileData.list.sort((a, b) => b.start_time - a.start_time); // 按时间倒序
          } else {
            this.logsList = [];
          }
        } else {
          this.logsList = [];
          this.$modal.msgError(response.message || "获取日志列表失败");
        }
        this.loading = false;
      }).catch(error => {
        this.logsList = [];
        this.loading = false;
        this.$modal.msgError("获取日志列表失败：请检查设备是否在线");
        console.error('获取日志列表出错:', error);
      });
    },
    
    // 刷新日志
    refreshLogs() {
      this.getLogsList();
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 获取设备类型名称
    getDeviceTypeName,

    // 获取设备类型标签样式
    getDeviceTypeTag,
    
    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },
    
    // 格式化持续时间
    formatDuration(startTime, endTime) {
      if (!startTime || !endTime) return '-';
      const duration = endTime - startTime;
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      const seconds = duration % 60;
      
      let result = '';
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分钟`;
      if (seconds > 0 || result === '') result += `${seconds}秒`;
      
      return result;
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 查看日志
    handleViewLog(row) {
      this.$modal.msgInfo(`查看日志功能待实现 - 启动索引: ${row.boot_index}`);
      // TODO: 实现日志查看功能
      console.log('查看日志:', row);
    },

    // 下载日志
    handleDownloadLog(row) {
      this.$modal.msgInfo(`下载日志功能待实现 - 启动索引: ${row.boot_index}`);
      // TODO: 实现日志下载功能
      console.log('下载日志:', row);
    }
  }
};
</script>

<style scoped>
.device-info-card {
  margin-bottom: 20px;
}

.device-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.info-item {
  margin-bottom: 10px;
}

.info-label {
  color: #909399;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  margin-left: 5px;
}

.logs-card {
  min-height: 400px;
}

.logs-card .el-table {
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}
</style>
