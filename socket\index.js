import WebSocketConfig from '../src/utils/websocket.js';

const socketService = {
    socket: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: -1, // 无限重连
    reconnectInterval: 10000,//10秒重连一次
    messageHandler: null, // 存储消息处理器
    reconnectCallbacks: [], // 重连成功后的回调函数

    init(username) {
        if (typeof WebSocket === "undefined") {
            alert("您的浏览器不支持WebSocket");
            return;
        }

        // 防止重复初始化
        if (this.socket && (this.socket.readyState === WebSocket.CONNECTING || this.socket.readyState === WebSocket.OPEN)) {
            console.log("WebSocket已存在且状态正常，跳过重复初始化");
            return;
        }

        // 如果存在旧连接，先关闭
        if (this.socket) {
            console.log("关闭旧的WebSocket连接");
            this.socket.close();
        }

        try {
            // 使用WebSocketConfig获取连接地址
            //const params = username ? { username } : {};
            const wsUrl = WebSocketConfig.getWebSocketUrlWithParams('/api/v1/ws');

            console.log("WebSocket连接地址:", wsUrl);

            this.socket = new WebSocket(wsUrl);
            this.socket.onopen = this.open.bind(this);
            this.socket.onerror = this.error.bind(this);
            this.socket.onmessage = this.messageHandler; // 使用存储的消息处理器
            this.socket.onclose = this.onClose.bind(this);
        } catch (error) {
            console.error("WebSocket初始化失败:", error);
        }
    },

    open() {
        console.log("WebSocket连接成功");
        const isReconnect = this.reconnectAttempts > 0;
        this.reconnectAttempts = 0; // 重置重连次数

        // 重连成功后，恢复消息处理器
        if (this.messageHandler) {
            this.socket.onmessage = this.messageHandler;
            if (isReconnect) {
                console.log("WebSocket重连成功，消息处理器已恢复");
            }
        }

        // 只在重连时执行重连回调，避免初次连接时的重复消息
        if (isReconnect) {
            this.reconnectCallbacks.forEach(callback => {
                try {
                    callback();
                } catch (error) {
                    console.error("执行重连回调失败:", error);
                }
            });
        }
    },

    error(error) {
        console.error("WebSocket连接错误:", error);
        this.reconnect();
    },

    onClose(event) {
        console.log("WebSocket连接关闭:", event.code, event.reason);
        if (event.code !== 1000) { // 非正常关闭
            this.reconnect();
        }
    },

    // 重连机制
    reconnect() {
        if (this.maxReconnectAttempts == -1 || this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => {
                this.init("warning-all");
            }, this.reconnectInterval);
        } else {
            console.error("WebSocket重连失败，已达到最大重连次数");
        }
    },

    getMessage() {
        return new Promise((resolve) => {
            this.socket.onmessage = (msg) => {

                // 利用promise 返回出去结果
                if (msg.data != '连接成功' && JSON.parse(msg.data)) {
                    const data = JSON.parse(msg.data);
                    resolve(data); // 将数据传递给调用者
                }
                // this.scrollInstance.refresh(); // 手动刷新滚动效果
            };
        });
        // this.scrollInstance.refresh(); // 手动刷新滚动效果
    },

    // 设置消息处理器
    setMessageHandler(handler) {
        this.messageHandler = handler;
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.onmessage = handler;
        }
    },

    // 添加重连回调
    addReconnectCallback(callback) {
        if (typeof callback === 'function') {
            this.reconnectCallbacks.push(callback);
        }
    },

    // 移除重连回调
    removeReconnectCallback(callback) {
        const index = this.reconnectCallbacks.indexOf(callback);
        if (index > -1) {
            this.reconnectCallbacks.splice(index, 1);
        }
    },

    send(params) {
        if (this.socket) {
            this.socket.send(params);
        }
    },

    close() {
        if (this.socket) {
            this.socket.close();
        }
        console.log("socket已经关闭");
    }
};

// 导出
export default socketService;