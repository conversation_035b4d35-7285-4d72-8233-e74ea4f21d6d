<template>
  <div class="status-icons-test">
    <h2>状态图标测试页面</h2>
    
    <!-- 模拟视频容器 -->
    <div class="video-container-test">
      <div class="video-debug-header">
        <el-button type="text" icon="el-icon-setting" class="debug-btn" size="mini">
          调试
        </el-button>
        
        <!-- 无人机状态图标 -->
        <div class="drone-status-icons" v-if="testOsdData">
          <!-- 电池状态图标 -->
          <el-tooltip 
            :content="getBatteryTooltip(testOsdData)" 
            placement="top"
            v-if="testOsdData.drone_charge_state">
            <div class="status-icon battery-icon" :class="getBatteryClass(testOsdData.drone_charge_state)">
              <i :class="getBatteryIcon(testOsdData.drone_charge_state)"></i>
              <span class="battery-percent">{{ testOsdData.drone_charge_state.capacity_percent }}%</span>
            </div>
          </el-tooltip>

          <!-- GPS/RTK状态图标 -->
          <el-tooltip 
            :content="getGpsTooltip(testOsdData)" 
            placement="top"
            v-if="testOsdData.position_state">
            <div class="status-icon gps-icon">
              <i class="el-icon-location"></i>
              <span class="gps-info">{{ getGpsDisplayText(testOsdData.position_state) }}</span>
            </div>
          </el-tooltip>

          <!-- 网络状态图标 -->
          <el-tooltip 
            :content="getNetworkTooltip(testOsdData)" 
            placement="top"
            v-if="testOsdData.network_state">
            <div class="status-icon network-icon">
              <i :class="getNetworkIcon(testOsdData.network_state)"></i>
              <span class="network-info">{{ getNetworkDisplayText(testOsdData.network_state) }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>
      
      <div class="video-placeholder">
        <p>视频播放区域</p>
        <p>设备SN: 7CTDM3900BM6WS</p>
      </div>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <h3>测试控制</h3>
      <el-button @click="toggleCharging">切换充电状态</el-button>
      <el-button @click="changeBatteryLevel">改变电池电量</el-button>
      <el-button @click="changeNetworkType">切换网络类型</el-button>
      <el-button @click="changeGpsSignal">改变GPS信号</el-button>
    </div>

    <!-- 数据显示 -->
    <div class="test-data">
      <h3>当前测试数据</h3>
      <pre>{{ JSON.stringify(testOsdData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatusIconsTest',
  data() {
    return {
      testOsdData: {
        drone_charge_state: {
          capacity_percent: 85,
          state: 1 // 1表示充电中，0表示空闲
        },
        position_state: {
          gps_number: 12,
          rtk_number: 8,
          quality: 5,
          is_calibration: 1,
          is_fixed: 2
        },
        network_state: {
          type: 1, // 1表示4G，2表示以太网
          quality: 4, // 0-5的质量等级
          rate: 1024.5 // KB/s
        }
      }
    };
  },
  methods: {
    // 获取电池图标
    getBatteryIcon(chargeState) {
      if (!chargeState) return 'el-icon-warning';

      const { state, capacity_percent } = chargeState;

      // 如果正在充电，显示充电图标
      if (state === 1) {
        return 'el-icon-lightning'; // 充电中
      }

      // 根据电量百分比显示不同的电池图标
      if (capacity_percent >= 75) {
        return 'el-icon-circle-check'; // 高电量
      } else if (capacity_percent >= 50) {
        return 'el-icon-warning-outline'; // 中等电量
      } else if (capacity_percent >= 25) {
        return 'el-icon-warning'; // 低电量
      } else {
        return 'el-icon-circle-close'; // 极低电量
      }
    },

    // 获取电池CSS类
    getBatteryClass(chargeState) {
      if (!chargeState) return '';

      const { state, capacity_percent } = chargeState;

      // 如果正在充电
      if (state === 1) {
        return 'charging';
      }

      // 如果电量低于25%
      if (capacity_percent < 25) {
        return 'low';
      }

      return '';
    },

    // 获取电池提示文本
    getBatteryTooltip(droneOsd) {
      const chargeState = droneOsd.drone_charge_state;
      if (!chargeState) return '电池状态未知';

      const { state, capacity_percent } = chargeState;
      const stateText = state === 1 ? '充电中' : '空闲';

      return `电池电量: ${capacity_percent}% (${stateText})`;
    },

    // 获取GPS显示文本
    getGpsDisplayText(positionState) {
      if (!positionState) return '--';

      const { gps_number, rtk_number } = positionState;

      // 显示GPS和RTK数量
      return `GPS:${gps_number || 0} RTK:${rtk_number || 0}`;
    },

    // 获取GPS提示文本
    getGpsTooltip(droneOsd) {
      const positionState = droneOsd.position_state;
      if (!positionState) return 'GPS状态未知';

      const {
        gps_number,
        rtk_number,
        quality,
        is_calibration,
        is_fixed
      } = positionState;

      const qualityMap = {
        1: '1档', 2: '2档', 3: '3档', 4: '4档', 5: '5档', 10: 'RTK fixed'
      };

      const calibrationText = is_calibration === 1 ? '已标定' : '未标定';
      const fixedMap = {
        0: '未开始', 1: '收敛中', 2: '收敛成功', 3: '收敛失败'
      };
      const fixedText = fixedMap[is_fixed] || '未知';

      return `GPS搜星: ${gps_number || 0}颗\nRTK搜星: ${rtk_number || 0}颗\n搜星档位: ${qualityMap[quality] || '未知'}\n标定状态: ${calibrationText}\n收敛状态: ${fixedText}`;
    },

    // 获取网络图标
    getNetworkIcon(networkState) {
      if (!networkState) return 'el-icon-warning';

      const { type } = networkState;

      // 根据网络类型显示不同图标
      if (type === 1) {
        return 'el-icon-mobile-phone'; // 4G
      } else if (type === 2) {
        return 'el-icon-connection'; // 以太网
      }

      return 'el-icon-connection';
    },

    // 获取网络显示文本
    getNetworkDisplayText(networkState) {
      if (!networkState) return '--';

      const { type, quality } = networkState;

      const typeMap = { 1: '4G', 2: '以太网' };
      const qualityMap = { 0: '无信号', 1: '差', 2: '较差', 3: '一般', 4: '较好', 5: '好' };

      return `${typeMap[type] || '未知'} ${qualityMap[quality] || '--'}`;
    },

    // 获取网络提示文本
    getNetworkTooltip(droneOsd) {
      const networkState = droneOsd.network_state;
      if (!networkState) return '网络状态未知';

      const { type, quality, rate } = networkState;

      const typeMap = { 1: '4G', 2: '以太网' };
      const qualityMap = { 0: '无信号', 1: '差', 2: '较差', 3: '一般', 4: '较好', 5: '好' };

      return `网络类型: ${typeMap[type] || '未知'}\n网络质量: ${qualityMap[quality] || '未知'}\n网络速率: ${rate ? rate.toFixed(1) + ' KB/s' : '--'}`;
    },

    // 测试控制方法
    toggleCharging() {
      this.testOsdData.drone_charge_state.state = this.testOsdData.drone_charge_state.state === 1 ? 0 : 1;
    },

    changeBatteryLevel() {
      const levels = [15, 35, 55, 85, 95];
      const currentLevel = this.testOsdData.drone_charge_state.capacity_percent;
      const currentIndex = levels.findIndex(level => level >= currentLevel);
      const nextIndex = (currentIndex + 1) % levels.length;
      this.testOsdData.drone_charge_state.capacity_percent = levels[nextIndex];
    },

    changeNetworkType() {
      this.testOsdData.network_state.type = this.testOsdData.network_state.type === 1 ? 2 : 1;
      this.testOsdData.network_state.quality = Math.floor(Math.random() * 6); // 0-5
    },

    changeGpsSignal() {
      this.testOsdData.position_state.gps_number = Math.floor(Math.random() * 20);
      this.testOsdData.position_state.rtk_number = Math.floor(Math.random() * 15);
      this.testOsdData.position_state.quality = Math.floor(Math.random() * 5) + 1; // 1-5
    }
  }
};
</script>

<style scoped>
.status-icons-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.video-container-test {
  position: relative;
  width: 600px;
  height: 400px;
  background: #000;
  border-radius: 8px;
  margin: 20px 0;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: white;
  font-size: 18px;
}

/* 调试按钮和状态图标样式 */
.video-debug-header {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8px;
}

.debug-btn {
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
}

/* 无人机状态图标容器 */
.drone-status-icons {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 状态图标通用样式 */
.status-icon {
  display: flex;
  align-items: center;
  gap: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  min-width: 0;
}

/* 电池图标样式 */
.battery-icon {
  background: rgba(76, 175, 80, 0.8); /* 绿色背景 */
}

.battery-icon.charging {
  background: rgba(255, 193, 7, 0.8); /* 充电时黄色背景 */
}

.battery-icon.low {
  background: rgba(244, 67, 54, 0.8); /* 低电量红色背景 */
}

.battery-percent {
  font-weight: bold;
  font-size: 10px;
}

/* GPS图标样式 */
.gps-icon {
  background: rgba(33, 150, 243, 0.8); /* 蓝色背景 */
}

.gps-info {
  font-size: 10px;
}

/* 网络图标样式 */
.network-icon {
  background: rgba(156, 39, 176, 0.8); /* 紫色背景 */
}

.network-info {
  font-size: 10px;
}

.test-controls {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-controls h3 {
  margin-top: 0;
}

.test-data {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-data h3 {
  margin-top: 0;
}

.test-data pre {
  background: white;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
