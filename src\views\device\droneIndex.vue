<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
       <el-form-item label="设备序列号" prop="device_sn">
        <el-input
          v-model="queryParams.device_sn"
          placeholder="请输入设备序列号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
     
      <el-form-item label="设备昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入设备昵称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      
      <el-form-item label="绑定状态" prop="bound_status">
        <el-select v-model="queryParams.bound_status" placeholder="请选择绑定状态" clearable style="width: 200px">
          <el-option label="未绑定" :value="false" />
          <el-option label="已绑定" :value="true" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['device:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['device:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['device:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['device:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="设备序列号" align="center" key="device_sn" prop="device_sn" v-if="columns[0].visible" :show-overflow-tooltip="true" />
      <el-table-column label="设备名称" align="center" key="device_name" prop="device_name" v-if="columns[1].visible" :show-overflow-tooltip="true" />
      <el-table-column label="设备昵称" align="center" key="nickname" prop="nickname" v-if="columns[2].visible" :show-overflow-tooltip="true" />

      <el-table-column label="设备类型" align="center" key="type" v-if="columns[6].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.domain === 0" type="primary">无人机</el-tag>
          <el-tag v-else-if="scope.row.domain === 2" type="primary">机场</el-tag>
          <el-tag v-else-if="scope.row.domain === 3" type="primary">机场</el-tag>
          <el-tag v-else-if="scope.row.domain === 4" type="primary">遥控器</el-tag>
          <el-tag v-else type="default">未知</el-tag>
        </template>
      </el-table-column>
 
      <el-table-column label="固件版本" align="center" key="firmware_version" prop="firmware_version" v-if="columns[10].visible" :show-overflow-tooltip="true" />
      <el-table-column label="固件状态" align="center" key="firmware_status" v-if="columns[11].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.firmware_status === 1" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 2" type="danger">升级</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 3" type="danger">一致升级</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 4" type="danger">升级期间</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="协议版本" align="center" key="thing_version" prop="thing_version" v-if="columns[12].visible" :show-overflow-tooltip="true" />
      <el-table-column label="绑定状态" align="center" key="bound_status" v-if="columns[13].visible">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.bound_status"
            :active-value="true"
            :inactive-value="false"
            @change="handleBoundStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="在线状态" align="center" key="mode_code" v-if="columns[16].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.mode_code >= 0" type="success">在线</el-tag>
          <el-tag v-else type="danger">离线</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="绑定时间" align="center" prop="bound_time" v-if="columns[14].visible" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.bound_time || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后登录" align="center" prop="login_time" v-if="columns[15].visible" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.login_time || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['device:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['device:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>


    <!-- 添加或修改无人机配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备序列号" prop="device_sn">
              <el-input v-model="form.device_sn" placeholder="请输入设备序列号" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="device_name">
              <el-input v-model="form.device_name" placeholder="请输入设备名称" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备昵称" prop="nickname">
              <el-input v-model="form.nickname" placeholder="请输入设备昵称" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作空间ID" prop="workspace_id">
              <el-input v-model="form.workspace_id" placeholder="请输入工作空间ID" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="控制源" prop="control_source">
              <el-select v-model="form.control_source" placeholder="请选择控制源">
                <el-option label="A控制" value="A" />
                <el-option label="B控制" value="B" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择设备类型">
                <el-option label="网关" :value="1" />
                <el-option label="机场" :value="2" />
                <el-option label="无人机" :value="3" />
                <el-option label="遥控器" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="子类型" prop="sub_type">
              <el-input-number v-model="form.sub_type" :min="0" placeholder="请输入子类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="域" prop="domain">
              <el-input-number v-model="form.domain" :min="1" placeholder="请输入域" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="子设备序列号" prop="child_device_sn">
              <el-input v-model="form.child_device_sn" placeholder="请输入子设备序列号" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="固件版本" prop="firmware_version">
              <el-input v-model="form.firmware_version" placeholder="请输入固件版本" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="固件状态" prop="firmware_status">
              <el-radio-group v-model="form.firmware_status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">异常</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议版本" prop="thing_version">
              <el-input v-model="form.thing_version" placeholder="请输入协议版本" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="绑定状态" prop="bound_status">
              <el-radio-group v-model="form.bound_status">
                <el-radio :label="true">已绑定</el-radio>
                <el-radio :label="false">未绑定</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="普通图标URL" prop="normal_icon_url">
              <el-input v-model="form.normal_icon_url" placeholder="请输入普通图标URL" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="选中图标URL" prop="selected_icon_url">
              <el-input v-model="form.selected_icon_url" placeholder="请输入选中图标URL" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备描述" prop="device_desc">
              <el-input v-model="form.device_desc" type="textarea" placeholder="请输入设备描述" maxlength="100"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 无人机设备详情对话框 -->
    <el-dialog title="无人机设备详情" :visible.sync="detailOpen" width="1200px" append-to-body>
      <div class="drone-detail-container">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-descriptions title="设备信息" :column="1" border>
                  <el-descriptions-item label="设备序列号">{{ currentDevice.device_sn }}</el-descriptions-item>
                  <el-descriptions-item label="设备名称">{{ currentDevice.device_name }}</el-descriptions-item>
                  <el-descriptions-item label="设备昵称">{{ currentDevice.nickname }}</el-descriptions-item>
                  <el-descriptions-item label="固件版本">{{ droneInfo.firmware_version }}</el-descriptions-item>
                  <el-descriptions-item label="激活时间">{{ formatTimestamp(droneInfo.activation_time) }}</el-descriptions-item>
                  <el-descriptions-item label="绑定状态">
                    <el-tag :type="currentDevice.bound_status ? 'success' : 'danger'">
                      {{ currentDevice.bound_status ? '已绑定' : '未绑定' }}
                    </el-tag>
                  </el-descriptions-item>
                </el-descriptions>
              </el-col>
              <el-col :span="12">
                <el-descriptions title="位置信息" :column="1" border>
                  <el-descriptions-item label="经度">{{ droneInfo.longitude }}°</el-descriptions-item>
                  <el-descriptions-item label="纬度">{{ droneInfo.latitude }}°</el-descriptions-item>
                  <el-descriptions-item label="高度">{{ droneInfo.height }}m</el-descriptions-item>
                  <el-descriptions-item label="海拔">{{ droneInfo.elevation }}m</el-descriptions-item>
                  <el-descriptions-item label="距离起飞点">{{ droneInfo.home_distance }}m</el-descriptions-item>
                  <el-descriptions-item label="GPS状态">
                    <el-tag :type="droneInfo.position_state && droneInfo.position_state.is_fixed === 2 ? 'success' : 'warning'">
                      {{ droneInfo.position_state && droneInfo.position_state.is_fixed === 2 ? 'RTK固定解' : 'GPS定位' }}
                    </el-tag>
                  </el-descriptions-item>
                </el-descriptions>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 飞行状态 -->
          <el-tab-pane label="飞行状态" name="flight">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>飞行姿态</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="航向角">{{ droneInfo.attitude_head }}°</el-descriptions-item>
                    <el-descriptions-item label="俯仰角">{{ droneInfo.attitude_pitch }}°</el-descriptions-item>
                    <el-descriptions-item label="横滚角">{{ droneInfo.attitude_roll }}°</el-descriptions-item>
                    <el-descriptions-item label="起落架状态">
                      <el-tag :type="droneInfo.gear === 1 ? 'success' : 'warning'">
                        {{ droneInfo.gear === 1 ? '收起' : '放下' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>飞行速度</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="水平速度">{{ droneInfo.horizontal_speed }}m/s</el-descriptions-item>
                    <el-descriptions-item label="垂直速度">{{ droneInfo.vertical_speed }}m/s</el-descriptions-item>
                    <el-descriptions-item label="风向">{{ getWindDirectionText(droneInfo.wind_direction) }}</el-descriptions-item>
                    <el-descriptions-item label="风速">{{ droneInfo.wind_speed }}m/s</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>飞行模式</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="飞行模式">
                      <el-tag :type="getFlightModeType()">{{ getFlightModeText() }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="夜航灯">
                      <el-tag :type="droneInfo.night_lights_state ? 'primary' : 'info'">
                        {{ droneInfo.night_lights_state ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="遥控失联动作">{{ getRcLostActionText() }}</el-descriptions-item>
                    <el-descriptions-item label="返航高度">{{ droneInfo.rth_altitude }}m</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 电池信息 -->
          <el-tab-pane label="电池信息" name="battery">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>电池状态</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="总电量">{{ droneInfo.battery && droneInfo.battery.capacity_percent }}%</el-descriptions-item>
                    <el-descriptions-item label="剩余飞行时间">{{ droneInfo.battery && droneInfo.battery.remain_flight_time }}分钟</el-descriptions-item>
                    <el-descriptions-item label="降落电量">{{ droneInfo.battery && droneInfo.battery.landing_power }}%</el-descriptions-item>
                    <el-descriptions-item label="返航电量">{{ droneInfo.battery && droneInfo.battery.return_home_power }}%</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>电池详情</span>
                  </div>
                  <el-table v-if="droneInfo.battery && droneInfo.battery.batteries" :data="droneInfo.battery.batteries" size="mini" border>
                    <el-table-column prop="index" label="电池编号" width="80"></el-table-column>
                    <el-table-column prop="capacity_percent" label="电量(%)" width="80"></el-table-column>
                    <el-table-column prop="voltage" label="电压(mV)" width="100"></el-table-column>
                    <el-table-column prop="temperature" label="温度(°C)" width="100"></el-table-column>
                    <el-table-column prop="loop_times" label="循环次数" width="100"></el-table-column>
                    <el-table-column prop="sn" label="序列号" :show-overflow-tooltip="true"></el-table-column>
                  </el-table>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 相机信息 -->
          <el-tab-pane label="相机信息" name="camera">
            <div v-if="droneInfo.cameras && droneInfo.cameras.length > 0">
              <el-card v-for="(camera, index) in droneInfo.cameras" :key="index" shadow="hover" style="margin-bottom: 20px;">
                <div slot="header">
                  <span>相机 {{ camera.payload_index }}</span>
                </div>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-descriptions title="基本状态" :column="1" border>
                      <el-descriptions-item label="相机模式">{{ getCameraModeText(camera.camera_mode) }}</el-descriptions-item>
                      <el-descriptions-item label="拍照状态">
                        <el-tag :type="camera.photo_state ? 'primary' : 'info'">
                          {{ camera.photo_state ? '拍照中' : '待机' }}
                        </el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="录像状态">
                        <el-tag :type="camera.recording_state ? 'danger' : 'info'">
                          {{ camera.recording_state ? '录像中' : '停止' }}
                        </el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="录像时长">{{ camera.record_time }}秒</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                  <el-col :span="8">
                    <el-descriptions title="变焦设置" :column="1" border>
                      <el-descriptions-item label="变焦倍数">{{ camera.zoom_factor }}x</el-descriptions-item>
                      <el-descriptions-item label="红外变焦">{{ camera.ir_zoom_factor }}x</el-descriptions-item>
                      <el-descriptions-item label="对焦模式">{{ getZoomFocusModeText(camera.zoom_focus_mode) }}</el-descriptions-item>
                      <el-descriptions-item label="对焦值">{{ camera.zoom_focus_value }}</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                  <el-col :span="8">
                    <el-descriptions title="存储信息" :column="1" border>
                      <el-descriptions-item label="剩余照片">{{ camera.remain_photo_num }}张</el-descriptions-item>
                      <el-descriptions-item label="剩余录像">{{ camera.remain_record_duration }}分钟</el-descriptions-item>
                      <el-descriptions-item label="分屏显示">
                        <el-tag :type="camera.screen_split_enable ? 'success' : 'info'">
                          {{ camera.screen_split_enable ? '开启' : '关闭' }}
                        </el-tag>
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </el-tab-pane>

          <!-- 限制设置 -->
          <el-tab-pane label="限制设置" name="limits">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>飞行限制</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="高度限制">{{ droneInfo.height_limit }}m</el-descriptions-item>
                    <el-descriptions-item label="接近高度限制">
                      <el-tag :type="droneInfo.is_near_height_limit ? 'warning' : 'success'">
                        {{ droneInfo.is_near_height_limit ? '是' : '否' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="距离限制状态">
                      <el-tag :type="droneInfo.distance_limit_status && droneInfo.distance_limit_status.state ? 'primary' : 'info'">
                        {{ droneInfo.distance_limit_status && droneInfo.distance_limit_status.state ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="距离限制">{{ droneInfo.distance_limit_status && droneInfo.distance_limit_status.distance_limit }}m</el-descriptions-item>
                    <el-descriptions-item label="接近距离限制">
                      <el-tag :type="droneInfo.distance_limit_status && droneInfo.distance_limit_status.is_near_distance_limit ? 'warning' : 'success'">
                        {{ droneInfo.distance_limit_status && droneInfo.distance_limit_status.is_near_distance_limit ? '是' : '否' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="接近区域限制">
                      <el-tag :type="droneInfo.is_near_area_limit ? 'warning' : 'success'">
                        {{ droneInfo.is_near_area_limit ? '是' : '否' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>避障设置</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="水平避障">
                      <el-tag :type="droneInfo.obstacle_avoidance && droneInfo.obstacle_avoidance.horizon ? 'success' : 'danger'">
                        {{ droneInfo.obstacle_avoidance && droneInfo.obstacle_avoidance.horizon ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="上方避障">
                      <el-tag :type="droneInfo.obstacle_avoidance && droneInfo.obstacle_avoidance.upside ? 'success' : 'danger'">
                        {{ droneInfo.obstacle_avoidance && droneInfo.obstacle_avoidance.upside ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="下方避障">
                      <el-tag :type="droneInfo.obstacle_avoidance && droneInfo.obstacle_avoidance.downside ? 'success' : 'danger'">
                        {{ droneInfo.obstacle_avoidance && droneInfo.obstacle_avoidance.downside ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 统计信息 -->
          <el-tab-pane label="统计信息" name="statistics">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>飞行统计</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="总飞行时间">{{ formatFlightTime(droneInfo.total_flight_time) }}</el-descriptions-item>
                    <el-descriptions-item label="总飞行距离">{{ formatDistance(droneInfo.total_flight_distance) }}</el-descriptions-item>
                    <el-descriptions-item label="总飞行架次">{{ droneInfo.total_flight_sorties }}次</el-descriptions-item>
                    <el-descriptions-item label="国家代码">{{ droneInfo.country }}</el-descriptions-item>
                    <el-descriptions-item label="RID状态">
                      <el-tag :type="droneInfo.rid_state ? 'success' : 'danger'">
                        {{ droneInfo.rid_state ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>存储信息</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="总容量">{{ formatStorage(droneInfo.storage && droneInfo.storage.total) }}</el-descriptions-item>
                    <el-descriptions-item label="已使用">{{ formatStorage(droneInfo.storage && droneInfo.storage.used) }}</el-descriptions-item>
                    <el-descriptions-item label="使用率">{{ getDroneStorageUsagePercent() }}%</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listDevice, getDevice, delDevice, addDevice, updateDevice, changeDeviceBindStatus, getDroneInfo } from "@/api/device";
import { SUCCESS_CODE } from "@/constants/responseCode";

export default {
  name: "DroneDevice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情对话框
      detailOpen: false,
      // 当前查看的设备
      currentDevice: {},
      // 详情页面活跃标签
      activeTab: 'basic',
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        device_sn: null,
        device_name: null,
        nickname: null,
        workspace_id: null,
        control_source: null,
        type: null,
        bound_status: null,
        deviceType: 0
      },
      // 列信息
      columns: [
        { key: 0, label: `设备序列号`, visible: true },
        { key: 1, label: `设备名称`, visible: true },
        { key: 2, label: `设备昵称`, visible: true },
        { key: 3, label: `工作空间ID`, visible: true },
        { key: 4, label: `工作空间名称`, visible: true },
        { key: 5, label: `控制源`, visible: true },
        { key: 6, label: `设备类型`, visible: true },
        { key: 7, label: `子类型`, visible: true },
        { key: 8, label: `域`, visible: true },
        { key: 9, label: `子设备序列号`, visible: true },
        { key: 10, label: `固件版本`, visible: true },
        { key: 11, label: `固件状态`, visible: true },
        { key: 12, label: `协议版本`, visible: true },
        { key: 13, label: `绑定状态`, visible: true },
        { key: 14, label: `绑定时间`, visible: true },
        { key: 15, label: `最后登录`, visible: true },
        { key: 16, label: `在线状态`, visible: true }
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        device_sn: [
          { required: true, message: "设备序列号不能为空", trigger: "blur" }
        ],
        device_name: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        nickname: [
          { required: true, message: "设备昵称不能为空", trigger: "blur" }
        ]
      },

droneInfo:{
		"attitude_head": 1,
		"attitude_pitch": 1.3,
		"attitude_roll": -0.5,
		"elevation": 0,
		"battery": {
			"batteries": [
				{
					"firmware_version": "02.00.20.58",
					"index": 0,
					"loop_times": 78,
					"capacity_percent": 91,
					"sn": "4BUPM3SDA420LQ",
					"sub_type": 0,
					"temperature": 34.8,
					"type": 0,
					"voltage": 24693,
					"high_voltage_storage_days": 104
				},
				{
					"firmware_version": "02.00.20.58",
					"index": 1,
					"loop_times": 78,
					"capacity_percent": 90,
					"sn": "4BUPM3SDA420LN",
					"sub_type": 0,
					"temperature": 35,
					"type": 0,
					"voltage": 24661,
					"high_voltage_storage_days": 107
				}
			],
			"capacity_percent": 90,
			"landing_power": 10,
			"remain_flight_time": 0,
			"return_home_power": 0
		},
		"firmware_version": "10.01.0504",
		"gear": 1,
		"height": 42.28282,
		"home_distance": 0.0139593305,
		"horizontal_speed": 0,
		"latitude": 28.181011,
		"longitude": 113.07996,
		"mode_code": 0,
		"total_flight_distance": 3267843.2989551853,
		"total_flight_time": 703602.06,
		"vertical_speed": 0,
		"wind_direction": 0,
		"wind_speed": 0,
		"position_state": {
			"gps_number": 13,
			"is_fixed": 2,
			"quality": 4,
			"rtk_number": 32
		},
		"storage": {
			"total": 243708000,
			"used": 261000
		},
		"night_lights_state": 0,
		"height_limit": 500,
		"distance_limit_status": {
			"state": 1,
			"distance_limit": 7000,
			"is_near_distance_limit": false
		},
		"obstacle_avoidance": {
			"horizon": 1,
			"upside": 1,
			"downside": 1
		},
		"activation_time": 1683665837,
		"cameras": [
			{
				"camera_mode": 0,
				"liveview_world_region": {
					"bottom": 0.76079667,
					"left": 0.27002937,
					"right": 0.7681199,
					"top": 0.26766866
				},
				"payload_index": "53-0-0",
				"photo_state": 0,
				"record_time": 0,
				"recording_state": 0,
				"remain_photo_num": 17575,
				"remain_record_duration": 0,
				"zoom_factor": 2,
				"ir_zoom_factor": 2,
				"screen_split_enable": false,
				"photo_storage_settings": [
					"current",
					"wide",
					"zoom",
					"ir"
				],
				"wide_exposure_mode": 1,
				"wide_iso": 10,
				"wide_shutter_speed": 45,
				"wide_exposure_value": 16,
				"zoom_exposure_mode": 1,
				"zoom_iso": 10,
				"zoom_shutter_speed": 45,
				"zoom_exposure_value": 16,
				"zoom_focus_mode": 2,
				"zoom_focus_value": 426,
				"zoom_max_focus_value": 256,
				"zoom_min_focus_value": 179,
				"ir_metering_mode": 0,
				"ir_metering_point": {
					"x": 0.5,
					"y": 0.5,
					"temperature": 0
				},
				"zoom_calibrate_farthest_focus_value": 199,
				"zoom_calibrate_nearest_focus_value": 236,
				"zoom_focus_state": 0
			}
		],
		"rc_lost_action": 2,
		"rth_altitude": 50,
		"total_flight_sorties": 1269,
		"country": "CN",
		"rid_state": true,
		"maintain_status": {
			"maintain_status_array": [
				{
					"last_maintain_flight_sorties": 0,
					"last_maintain_flight_time": 0,
					"last_maintain_time": 0,
					"last_maintain_type": 1,
					"state": false
				},
				{
					"last_maintain_flight_sorties": 0,
					"last_maintain_flight_time": 0,
					"last_maintain_time": 0,
					"last_maintain_type": 2,
					"state": false
				},
				{
					"last_maintain_flight_sorties": 0,
					"last_maintain_flight_time": 0,
					"last_maintain_time": 0,
					"last_maintain_type": 3,
					"state": false
				}
			]
		},
		"track_id": "",
		"parent_sn": "4TADL330010050",
		"payload": [
			{
				"payload_index": "53-0-0",
				"gimbal_pitch": -0.2,
				"gimbal_roll": 0.1,
				"gimbal_yaw": 1.3000001,
				"measure_target_altitude": 0,
				"measure_target_distance": 0,
				"measure_target_latitude": 0,
				"measure_target_longitude": 0,
				"measure_target_error_state": 1,
				"version": 1,
				"thermal_current_palette_style": 12,
				"thermal_gain_mode": 1,
				"thermal_global_temperature_max": 31.06225,
				"thermal_global_temperature_min": 26.717249,
				"thermal_isotherm_lower_limit": 0,
				"thermal_isotherm_state": 0,
				"thermal_isotherm_upper_limit": 500
			}
		],
		"is_near_area_limit": true,
		"is_near_height_limit": false
	}

    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        // 根据实际API返回结构处理数据
        if (response.code === SUCCESS_CODE) {
          this.deviceList = response.data || [];
          this.total = response.data ? response.data.length : 0;
        } else {
          this.deviceList = [];
          this.total = 0;
          this.$modal.msgError(response.message || "获取设备列表失败");
        }
        this.loading = false;
      }).catch(() => {
        this.deviceList = [];
        this.total = 0;
        this.loading = false;
        this.$modal.msgError("获取设备列表失败");
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        device_sn: null,
        device_name: null,
        nickname: null,
        workspace_id: null,
        control_source: null,
        device_desc: null,
        child_device_sn: null,
        domain: 3,
        type: 3, // 默认设置为无人机类型
        sub_type: 0,
        bound_status: false,
        firmware_version: null,
        firmware_status: 1,
        thing_version: null,
        normal_icon_url: "",
        selected_icon_url: "",
        mode_code:""
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.device_sn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加无人机";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const device_sn = row.device_sn || this.ids[0];
      if (row.device_sn) {
        // 直接使用行数据，处理icon_url嵌套结构
        this.form = Object.assign({}, row);
        if (row.icon_url) {
          this.form.normal_icon_url = row.icon_url.normal_icon_url || "";
          this.form.selected_icon_url = row.icon_url.selected_icon_url || "";
        }
        this.open = true;
        this.title = "修改无人机";
      } else {
        // 从API获取数据
        getDevice(device_sn).then(response => {
          if (response.code === SUCCESS_CODE) {
            this.form = Object.assign({}, response.data);
            if (response.data.icon_url) {
              this.form.normal_icon_url = response.data.icon_url.normal_icon_url || "";
              this.form.selected_icon_url = response.data.icon_url.selected_icon_url || "";
            }
          } else {
            this.form = Object.assign({}, row);
            if (row.icon_url) {
              this.form.normal_icon_url = row.icon_url.normal_icon_url || "";
              this.form.selected_icon_url = row.icon_url.selected_icon_url || "";
            }
          }
          this.open = true;
          this.title = "修改无人机";
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 构建提交数据，重新组装icon_url结构
          const submitData = Object.assign({}, this.form);
          submitData.icon_url = {
            normal_icon_url: this.form.normal_icon_url || "",
            selected_icon_url: this.form.selected_icon_url || ""
          };
          // 删除临时字段
          delete submitData.normal_icon_url;
          delete submitData.selected_icon_url;

          if (this.title === "修改无人机") {
            updateDevice(submitData).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(submitData).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const device_sns = row.device_sn ? [row.device_sn] : this.ids;
      const deviceNames = row.device_sn ? [row.nickname || row.device_name] :
        this.deviceList.filter(item => device_sns.includes(item.device_sn))
          .map(item => item.nickname || item.device_name);

      this.$modal.confirm('是否确认删除无人机"' + deviceNames.join('、') + '"？').then(function() {
        return delDevice(device_sns.join(','));
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 设备绑定状态修改
    handleBoundStatusChange(row) {
      let text = row.bound_status === true ? "绑定" : "解绑";
      this.$modal.confirm('确认要"' + text + '""' + row.nickname + '"无人机吗？').then(function() {
        return changeDeviceBindStatus(row.device_sn, row.bound_status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.bound_status = !row.bound_status;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('device/export', {
        ...this.queryParams
      }, `drone_device_${new Date().getTime()}.xlsx`)
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.currentDevice = Object.assign({}, row);
      this.activeTab = 'basic';
      this.detailOpen = true;
      // 获取无人机设备详细信息
      this.getDroneDeviceInfo(row.device_sn);
    },
    /** 获取无人机设备信息 */
    getDroneDeviceInfo(droneSn) {
      if (!droneSn) return;
      getDroneInfo(droneSn).then(response => {
        if (response.code === SUCCESS_CODE && response.data) {
          this.droneInfo = response.data;
        } else {
          console.warn('获取无人机设备信息失败:', response.message);
        }
      }).catch(error => {
        console.error('获取无人机设备信息出错:', error);
      });
    },
    // 获取风向文本
    getWindDirectionText(direction) {
      const directionMap = {
        0: '无风',
        1: '北风',
        2: '东北风',
        3: '东风',
        4: '东南风',
        5: '南风',
        6: '西南风',
        7: '西风',
        8: '西北风'
      };
      return directionMap[direction] || '未知';
    },
    // 获取飞行模式类型
    getFlightModeType() {
      const mode = this.droneInfo.mode_code;
      if (mode === 0) return 'success'; // 待机
      if (mode >= 1 && mode <= 10) return 'primary'; // 飞行中
      return 'warning'; // 其他状态
    },
    // 获取飞行模式文本
    getFlightModeText() {
      const modeMap = {
        0: '待机',
        1: '手动模式',
        2: '姿态模式',
        3: 'GPS模式',
        4: '智能模式',
        5: '运动模式',
        6: '电影模式',
        7: '三脚架模式',
        8: '返航模式',
        9: '降落模式',
        10: '自动模式'
      };
      return modeMap[this.droneInfo.mode_code] || '未知模式';
    },
    // 获取遥控失联动作文本
    getRcLostActionText() {
      const actionMap = {
        0: '悬停',
        1: '降落',
        2: '返航'
      };
      return actionMap[this.droneInfo.rc_lost_action] || '未知';
    },
    // 获取相机模式文本
    getCameraModeText(mode) {
      const modeMap = {
        0: '拍照模式',
        1: '录像模式',
        2: '回放模式'
      };
      return modeMap[mode] || '未知模式';
    },
    // 获取对焦模式文本
    getZoomFocusModeText(mode) {
      const modeMap = {
        0: '手动对焦',
        1: '自动对焦',
        2: '连续对焦'
      };
      return modeMap[mode] || '未知';
    },
    // 格式化存储容量
    formatStorage(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    // 获取无人机存储使用率
    getDroneStorageUsagePercent() {
      if (!this.droneInfo.storage || !this.droneInfo.storage.total || !this.droneInfo.storage.used) return 0;
      return ((this.droneInfo.storage.used / this.droneInfo.storage.total) * 100).toFixed(1);
    },
    // 格式化飞行时间
    formatFlightTime(seconds) {
      if (!seconds) return '0秒';
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      let result = '';
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分钟`;
      if (secs > 0 || result === '') result += `${secs}秒`;

      return result;
    },
    // 格式化距离
    formatDistance(meters) {
      if (!meters) return '0m';
      if (meters >= 1000) {
        return (meters / 1000).toFixed(2) + 'km';
      }
      return meters.toFixed(2) + 'm';
    },
    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }
};
</script>

<style scoped>
.drone-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.drone-detail-container .el-card {
  margin-bottom: 10px;
}

.drone-detail-container .el-descriptions {
  margin-bottom: 10px;
}

.drone-detail-container .el-table {
  margin-top: 10px;
}

.drone-detail-container .el-tabs__content {
  padding: 20px;
}

.drone-detail-container .el-row {
  margin-bottom: 20px;
}

.drone-detail-container .el-col {
  margin-bottom: 10px;
}
</style>
