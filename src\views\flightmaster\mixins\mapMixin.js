/**
 * 地图处理Mixin
 * 提供地图相关功能，包括坐标转换、区域绘制、城市名称获取等
 */
/* global AMap */
import { rectangle } from '@/api/system/dict/data';
import axios from 'axios';

// 常量定义
const PI = 3.14159265358979324;
const A = 6378245.0;
const EE = 0.00669342162296594323;

export default {
  data() {
    return {
      cityNames: [], // 新增属性，用于存储城市名称
      infoWindow: null, // 用于存储信息窗体实例
      selectedRegion: { name: '' }
    };
  },

  methods: {
    // API 2.0 优化：通过经纬度获取城市名称
    async fetchCityNames() {
      console.log("获取禁飞区数据this.deviceList", this.deviceList);

      // 从设备列表中提取坐标信息
      const deviceCoordinates = this.deviceList
        .filter(device => device.longitude && device.latitude)
        .map(device => [device.longitude, device.latitude]);

      console.log("获取禁飞区数据deviceCoordinates", deviceCoordinates);

      if (deviceCoordinates.length === 0) {
        return;
      }

      try {
        // API 2.0 优化：使用 Promise.allSettled 处理并发请求
        const promises = deviceCoordinates.map(async (coord) => {
          const [lng, lat] = coord;
          try {
            const response = await axios.get(
              `https://res.abeim.cn/api-location_geocoder_address?lng=${lng}&lat=${lat}`,
              { timeout: 5000 } // 添加超时设置
            );
            return response.data.code === 200 ? response.data.data.city : null;
          } catch (error) {
            console.warn(`获取城市名称失败 [${lng}, ${lat}]:`, error.message);
            return null;
          }
        });

        // 等待所有请求完成，使用 allSettled 避免单个请求失败影响整体
        const results = await Promise.allSettled(promises);
        this.cityNames = [...new Set(
          results
            .filter(result => result.status === 'fulfilled' && result.value)
            .map(result => result.value)
        )];

        console.log("获取到的城市列表:", this.cityNames);

        // 为每个城市获取禁飞区数据
        await this.fetchNoFlyZones();

      } catch (error) {
        console.error('获取城市名称失败:', error);
      }
    },

    // API 2.0 新增：批量获取禁飞区数据
    async fetchNoFlyZones() {
      const map = this.$refs.amap?.map;
      if (!map) {
        console.warn('地图实例未初始化');
        return;
      }

      try {
        // 并发获取所有城市的禁飞区数据
        const promises = this.cityNames.map(async (city) => {
          try {
            const response = await rectangle(city);
            return { city, data: response };
          } catch (error) {
            console.warn(`获取 ${city} 禁飞区数据失败:`, error.message);
            return { city, data: null };
          }
        });

        const results = await Promise.allSettled(promises);

        // 绘制所有有效的禁飞区数据
        results.forEach(result => {
          if (result.status === 'fulfilled' && result.value.data?.data?.areas) {
            this.drawRegions(result.value.data, map);
          }
        });

      } catch (error) {
        console.error('批量获取禁飞区数据失败:', error);
      }
    },

   

    // 创建自定义信息窗体内容
    createInfoWindowContent(name, level, color, height) {
      // 创建一个包含区域信息的自定义内容
      const content = document.createElement('div');
      content.className = 'custom-info-window';

      // 添加区域名称
      const areaTitle = document.createElement('div');
      areaTitle.className = 'info-area-title';
      areaTitle.innerHTML = `<span class="info-label">区域：</span>${name || '未知区域'}`;
      content.appendChild(areaTitle);

      // 根据颜色确定区域类型和样式类
      let areaType = '未知区域';
      let styleClass = 'warning-level';

      if (color === '#DE4329') {
        areaType = '禁飞区';
        styleClass = 'no-fly-zone';
      } else if (color === '#979797') {
        areaType = '限高区';
        styleClass = 'height-restricted-zone';
      } else if (color === '#EE8815') {
        areaType = '加强警示区';
        styleClass = 'warning-zone';
      }

      // 添加等级信息
      const levelInfo = document.createElement('div');
      levelInfo.className = 'info-level';
      levelInfo.innerHTML = `<span class="info-label">等级：</span><span class="${styleClass}">${areaType}</span>`;
      content.appendChild(levelInfo);

      // 如果是限高区，添加限高信息
      if (color === '#979797' && height) {
        const heightInfo = document.createElement('div');
        heightInfo.className = 'info-height';
        heightInfo.innerHTML = `<span class="info-label">限高：</span><span class="height-value">${height}米</span>`;
        content.appendChild(heightInfo);
      }

      return content;
    },

    // API 2.0 优化：绘制圆形区域和多边形区域
    drawRegions(regions, map) {
      if (!regions?.data?.areas || !Array.isArray(regions.data.areas)) {
        console.warn('无效的区域数据');
        return;
      }

      // 首先按面积大小对区域进行排序
      const sortedAreas = [...regions.data.areas].sort((a, b) => {
        if (a.shape === 0 && b.shape === 0) {
          return b.radius - a.radius; // 大区域在前
        }
        return 0;
      });

      // 初始化信息窗体（如果还没有）
      this.initInfoWindow(map);

      // 按照从大到小的顺序绘制区域
      sortedAreas.forEach((region, index) => {
        const baseZIndex = 50;
        const zIndexValue = baseZIndex + index * 10;

        try {
          if (region.shape === 0) {
            this.drawCircleRegion(region, map, zIndexValue);
          } else if (region.shape === 1) {
            this.drawPolygonRegion(region, map, zIndexValue);
          }
        } catch (error) {
          console.error('绘制区域失败:', error, region);
        }
      });
    },

    // API 2.0 新增：初始化信息窗体
    initInfoWindow(map) {
      if (!this.infoWindow) {
        this.infoWindow = new AMap.InfoWindow({
          offset: new AMap.Pixel(0, -30),
          closeWhenClickMap: true,
          autoMove: true,
          // API 2.0 新增配置
          anchor: 'bottom-center',
          retainWhenClose: true
        });
      }
    },

    // API 2.0 新增：绘制圆形区域
    drawCircleRegion(region, map, zIndex) {
      const gcj02Coord = this.wgs84ToGcj02(region.lng, region.lat);

      const circle = new AMap.Circle({
        center: [gcj02Coord.lng, gcj02Coord.lat],
        radius: region.radius,
        strokeColor: region.color,
        strokeWeight: 2,
        strokeOpacity: 0.8,
        fillColor: region.color,
        fillOpacity: 0.35,
        zIndex: zIndex,
        cursor: 'pointer',
        // API 2.0 优化：更好的交互效果
        bubble: true,
        extData: {
          name: region.name,
          level: region.level,
          color: region.color,
          height: region.height,
          type: 'circle'
        }
      });

      // 绑定事件
      this.bindRegionEvents(circle, map);
      map.add(circle);
    },

    // API 2.0 新增：绘制多边形区域
    drawPolygonRegion(region, map, zIndex) {
      if (!region.polygon_points || !Array.isArray(region.polygon_points)) {
        console.warn('无效的多边形数据:', region);
        return;
      }

      region.polygon_points.forEach((polygon, polygonIndex) => {
        try {
          // 转换坐标系
          const convertedPolygon = polygon.map(point => {
            const gcj02Coord = this.wgs84ToGcj02(point[0], point[1]);
            return [gcj02Coord.lng, gcj02Coord.lat];
          });

          // 检查多边形是否有效（至少3个点）
          if (convertedPolygon.length >= 3) {
            const polygonShape = new AMap.Polygon({
              path: convertedPolygon,
              strokeColor: region.color,
              strokeWeight: 2,
              strokeOpacity: 0.8,
              fillColor: region.color,
              fillOpacity: 0.35,
              zIndex: zIndex + polygonIndex,
              cursor: 'pointer',
              // API 2.0 优化配置
              bubble: true,
              extData: {
                name: region.name,
                level: region.level,
                color: region.color,
                height: region.height,
                type: 'polygon'
              }
            });

            // 绑定事件
            this.bindRegionEvents(polygonShape, map);
            map.add(polygonShape);
          }
        } catch (error) {
          console.error('绘制多边形失败:', error, polygon);
        }
      });
    },

    // API 2.0 新增：绑定区域事件
    bindRegionEvents(shape, map) {
      // 点击事件
      shape.on('click', (e) => {
        const extData = e.target.getExtData();
        const content = this.createInfoWindowContent(
          extData.name,
          extData.level,
          extData.color,
          extData.height
        );
        this.infoWindow.setContent(content);
        this.infoWindow.open(map, e.lnglat);
      });

      // API 2.0 新增：鼠标悬停效果
      shape.on('mouseover', (e) => {
        const target = e.target;
        target.setOptions({
          strokeWeight: 3,
          fillOpacity: 0.5
        });
      });

      shape.on('mouseout', (e) => {
        const target = e.target;
        target.setOptions({
          strokeWeight: 2,
          fillOpacity: 0.35
        });
      });
    },




 // 判断是否在中国境内
    outOfChina(lng, lat) {
      if (lng < 72.004 || lng > 137.8347) {
        return true;
      }
      if (lat < 0.8293 || lat > 55.8271) {
        return true;
      }
      return false;
    },

    // 转换经纬度
    transformLat(x, y) {
      let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
      ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
      return ret;
    },

    transformLon(x, y) {
      let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
      ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
      ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
      ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
      return ret;
    },

    // WGS-84到GCJ-02坐标转换
    wgs84ToGcj02(lng, lat) {
      if (this.outOfChina(lng, lat)) {
        return { lng, lat };
      }
      let dLat = this.transformLat(lng - 105.0, lat - 35.0);
      let dLon = this.transformLon(lng - 105.0, lat - 35.0);
      let radLat = lat / 180.0 * PI;
      let magic = Math.sin(radLat);
      magic = 1 - EE * magic * magic;
      let sqrtMagic = Math.sqrt(magic);
      dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
      dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
      let mgLat = lat + dLat;
      let mgLng = lng + dLon;
      return { lng: mgLng, lat: mgLat };
    },
 // GCJ02 转 WGS84 坐标系转换
    gcj02ToWgs84(lng, lat) {
      if (this.outOfChina(lng, lat)) {
        return { lng, lat };
      }
      let dLat = this.transformLat(lng - 105.0, lat - 35.0);
      let dLon = this.transformLon(lng - 105.0, lat - 35.0);
      const PI = 3.14159265358979324;
      const A = 6378245.0;
      const EE = 0.00669342162296594323;
      let radLat = lat / 180.0 * PI;
      let magic = Math.sin(radLat);
      magic = 1 - EE * magic * magic;
      let sqrtMagic = Math.sqrt(magic);
      dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
      dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
      let mgLat = lat - dLat;
      let mgLng = lng - dLon;
      return { lng: mgLng, lat: mgLat };
    },



  }
};
