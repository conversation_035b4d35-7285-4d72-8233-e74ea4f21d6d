/**
 * WebSocket处理Mixin
 * 提供WebSocket连接、消息处理、设备状态管理等功能
 */
import { getToken } from "@/utils/auth";
import socketService from '../../../../socket/index';

export default {
  data() {
    return {
      // WebSocket连接状态
      websocketStatus: 'disconnected', // disconnected, connecting, connected, error
      websocketError: null,
      reconnectCallback: null, // WebSocket重连回调函数引用

      // WebSocket OSD数据
      osdDock: {},
      osdDrone: {}, // 存储无人机OSD数据

      // 设备最后更新时间跟踪
      deviceLastUpdateTime: {}, // 存储设备最后更新时间 {deviceSn: timestamp}

      // 超时检测定时器
      timeoutCheckTimer: null,

      // 超时时间设置（毫秒）
      DEVICE_TIMEOUT: 5000 // 5秒
    };
  },

  methods: {
    // 新增连接 WebSocket 的方法
    async connectWebSocket() {
      // 防止重复连接
      if (this.websocketStatus === 'connected' || this.websocketStatus === 'connecting') {
        console.log("WebSocket已连接或正在连接中，跳过重复连接");
        return;
      }

      const token = getToken(); // 获取实际的 Authorization token
      console.log("开始连接WebSocket，token:", token);

      this.websocketStatus = 'connecting';
      this.websocketError = null;

      try {
        // 设置消息处理器（在连接前设置，确保重连时也能使用）
        console.log("设置WebSocket消息处理器...");
        this.setupMessageHandler();

        // 清理之前的重连回调（防止重复添加）
        if (this.reconnectCallback) {
          socketService.removeReconnectCallback(this.reconnectCallback);
        }

        // 添加重连回调
        this.reconnectCallback = () => {
          console.log("WebSocket重连成功，更新状态");
          this.websocketStatus = 'connected';
          this.websocketError = null;
          this.$message.success("WebSocket重连成功");
        };
        socketService.addReconnectCallback(this.reconnectCallback);

        // 初始化WebSocket连接
        console.log("初始化WebSocket连接...");
        socketService.init("warning-all");

        // 等待连接建立
        console.log("等待WebSocket连接建立...");
        await this.waitForConnection();

        this.websocketStatus = 'connected';
        console.log("WebSocket连接和消息处理器设置完成");
        this.$message.success("WebSocket连接成功");
      } catch (error) {
        console.error("WebSocket连接失败:", error);
        this.websocketStatus = 'error';
        this.websocketError = error.message;
        this.$message.error(`WebSocket连接失败: ${error.message}`);
      }
    },

    // 等待WebSocket连接建立
    waitForConnection() {
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (socketService.socket) {
            if (socketService.socket.readyState === WebSocket.OPEN) {
              console.log("WebSocket连接已建立");
              resolve();
            } else if (socketService.socket.readyState === WebSocket.CLOSED ||
                      socketService.socket.readyState === WebSocket.CLOSING) {
              reject(new Error("WebSocket连接失败"));
            } else {
              // 连接中，继续等待
              setTimeout(checkConnection, 100);
            }
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();

        // 设置超时
        setTimeout(() => {
          reject(new Error("WebSocket连接超时"));
        }, 10000);
      });
    },

    // 设置消息处理器
    setupMessageHandler() {
      console.log("设置WebSocket消息处理器");

      // 创建消息处理器函数
      const messageHandler = (event) => {
        try {
          //console.log("收到原始WebSocket消息:", event.data);
          this.handleWebSocketMessage(JSON.parse(event.data));
        } catch (error) {
          console.error("处理WebSocket消息失败:", error, event.data);
        }
      };

      // 使用socketService的新API设置消息处理器
      socketService.setMessageHandler(messageHandler);
      console.log("WebSocket消息处理器已设置");
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      try {
        // 检查是否是dock_osd消息
        if (data && data.biz_code === 'dock_osd') {
          this.handleDockOsdMessage(data);
        }
        // 检查是否是device_osd消息（无人机OSD）
        if (data && data.biz_code === 'device_osd') {
          this.handleDroneOsdMessage(data);
        }
        //设备上线
        if (data && data.biz_code === 'device_online') {
          console.log("设备上线了");
          this.$message.success(`设备上线: ${data.sn}`);
        }
        if (data && data.biz_code === 'device_offline') {
          this.handleDeviceOfflineMessage(data);
        }
      } catch (error) {
        console.error('处理WebSocket消息失败:', error, data);
      }
    },

    // 处理dock_osd消息
    handleDockOsdMessage(message) {
      try {
        const { data: messageData } = message;
        if (messageData && messageData.host) {
          const sn = messageData.sn;
          // 如果osdDock中还没有该设备的数据，初始化
          if (!this.osdDock[sn]) {
            this.$set(this.osdDock, sn, {});
          }
          // 合并host数据到osdDock
          this.$set(this.osdDock, sn, {
            ...this.osdDock[sn],
            ...messageData.host,
            lastUpdate: Date.now(), // 添加最后更新时间
            sn: sn // 保存设备SN
          });

          // 更新deviceList中对应设备的信息
          this.updateDeviceFromOsd(sn, messageData.host);
          // 更新设备最后更新时间
          this.updateDeviceLastUpdateTime(sn);
        }
      } catch (error) {
        console.error('处理dock_osd消息失败:', error);
      }
    },

    // 从OSD数据更新设备列表信息
    updateDeviceFromOsd(sn, hostData) {
      try {
        if (!this.deviceList || !Array.isArray(this.deviceList)) {
          console.warn('deviceList不存在或不是数组');
          return;
        }
        // 查找对应的设备
        const device = this.deviceList.find(d => d.device_sn === sn);

        if (device) {
          // 安全更新mode_code
          if (hostData.mode_code !== undefined && hostData.mode_code !== null) {
            this.$set(device, 'mode_code', hostData.mode_code);
          }
          // 安全更新task_name
          if (hostData.task_name !== undefined && hostData.task_name !== null) {
            this.$set(device, 'task_name', hostData.task_name);
          }
          // 更新其他可能需要的字段
          if (hostData.drc_state !== undefined && hostData.drc_state !== null) {
            this.$set(device, 'drc_state', hostData.drc_state);
          }

          // 更新机场经纬度坐标
          if (hostData.longitude !== undefined && hostData.longitude !== null) {
            const oldLng = device.longitude;
            const oldLat = device.latitude;
            this.$set(device, 'longitude', hostData.longitude);
            this.$set(device, 'latitude', hostData.latitude);

            // 检查坐标是否发生较大变化（超过0.001度，约100米）
            const lngDiff = Math.abs(hostData.longitude - oldLng);
            const latDiff = Math.abs(hostData.latitude - oldLat);
            if (lngDiff > 0.001 || latDiff > 0.001) {
              console.log(`机场坐标发生较大变化: ${device.nickname}`, {
                old: [oldLng, oldLat],
                new: [hostData.longitude, hostData.latitude]
              });
              // 更新地图标记位置
              if (this.createOrUpdateDeviceMarker) {
                this.createOrUpdateDeviceMarker(device);
              }
            }
          }

        } else {
          console.warn(`未找到SN为 ${sn} 的设备`);
        }
      } catch (error) {
        console.error('更新设备信息失败:', error);
      }
    },

    // 处理device_osd消息（无人机OSD）
    handleDroneOsdMessage(message) {
      try {
        const { data: messageData } = message;
        if (messageData && messageData.host) {
          const droneSn = messageData.sn; // 无人机SN
          const parentSn = messageData.host.parent_sn; // 机场SN（可能不存在）
          const droneMode = messageData.host.mode_code; // 无人机mode_code

          // 存储完整的无人机OSD数据
          if (!this.osdDrone[droneSn]) {
            this.$set(this.osdDrone, droneSn, {});
          }

          // 合并无人机OSD数据
          this.$set(this.osdDrone, droneSn, {
            ...this.osdDrone[droneSn],
            ...messageData.host,
            lastUpdate: Date.now(),
            sn: droneSn
          });

          // 检查deviceList是否存在
          if (!this.deviceList || !Array.isArray(this.deviceList)) {
            console.warn('deviceList不存在或不是数组，无法处理无人机OSD消息');
            return;
          }

          // 查找对应的机场设备
          let dockDevice = null;

          // 方法2：如果没有parent_sn或parent_sn不匹配，遍历所有机场设备查找匹配的无人机


            dockDevice = this.deviceList.find(device =>
              device.children && device.children.device_sn === droneSn
            );

          // 如果找到了匹配的机场设备，更新无人机信息
          if (dockDevice && dockDevice.children && dockDevice.children.device_sn === droneSn) {
            // 更新无人机的mode_code
            this.$set(dockDevice.children, 'mode_code', droneMode);

            // 更新无人机经纬度坐标
            const hostData = messageData.host;
            if (hostData.longitude !== undefined && hostData.longitude !== null) {
              const oldLng = dockDevice.children.longitude;
              const oldLat = dockDevice.children.latitude;
              this.$set(dockDevice.children, 'longitude', hostData.longitude);
              this.$set(dockDevice.children, 'latitude', hostData.latitude);

              // 检查坐标是否发生较大变化（超过0.001度，约100米）
              const lngDiff = Math.abs(hostData.longitude - (oldLng || 0));
              const latDiff = Math.abs(hostData.latitude - (oldLat || 0));
              if (lngDiff > 0.001 || latDiff > 0.001) {
                console.log(`无人机坐标发生较大变化: ${dockDevice.children.nickname}`, {
                  old: [oldLng, oldLat],
                  new: [hostData.longitude, hostData.latitude]
                });
                // 无人机标记暂时不在地图上显示，如需要可以添加
                // if (this.createOrUpdateDeviceMarker) {
                //   this.createOrUpdateDeviceMarker(dockDevice.children);
                // }
              }
            }

            // 更新无人机最后更新时间
            this.updateDeviceLastUpdateTime(droneSn);


          } else {
            // 提供更详细的调试信息
            console.warn('未找到对应的机场或无人机设备:', {
              droneSn,
              parentSn: parentSn || '未提供',
              deviceListCount: this.deviceList ? this.deviceList.length : 0,
              availableDevices: this.deviceList ? this.deviceList.map(d => ({
                device_sn: d.device_sn,
                nickname: d.nickname,
                children_sn: d.children ? d.children.device_sn : null,
                children_nickname: d.children ? d.children.nickname : null
              })) : []
            });
          }
        }
      } catch (error) {
        console.error('处理device_osd消息失败:', error, message);
      }
    },

    // 处理设备离线消息
    handleDeviceOfflineMessage(message) {
      try {
        const { data: messageData } = message;
        if (messageData && messageData.sn) {
          const deviceSn = messageData.sn;
          console.log('收到设备离线消息:', deviceSn);

          // 根据设备SN查找是无人机还是机场
          this.setDeviceOffline(deviceSn);

          this.$message.warning(`设备离线: ${deviceSn}`);
        }
      } catch (error) {
        console.error('处理设备离线消息失败:', error, message);
      }
    },

    // 设置设备为离线状态
    setDeviceOffline(deviceSn) {
      try {
        // 首先检查是否是机场设备
        const dockDevice = this.deviceList.find(device => device.device_sn === deviceSn);
        if (dockDevice) {
          // 是机场设备，设置为离线
          this.$set(dockDevice, 'mode_code', -1);
          console.log('设置机场离线:', deviceSn);
          return;
        }

        // 检查是否是无人机设备
        for (const dock of this.deviceList) {
          if (dock.children && dock.children.device_sn === deviceSn) {
            // 是无人机设备，设置为离线
            this.$set(dock.children, 'mode_code', 14);
            console.log('设置无人机离线:', deviceSn);
            return;
          }
        }

        console.warn('未找到对应的设备:', deviceSn);
      } catch (error) {
        console.error('设置设备离线状态失败:', error);
      }
    },

    // 更新设备最后更新时间
    updateDeviceLastUpdateTime(deviceSn) {
      this.$set(this.deviceLastUpdateTime, deviceSn, Date.now());
    },

    // 启动超时检测
    startTimeoutCheck() {
      if (this.timeoutCheckTimer) {
        clearInterval(this.timeoutCheckTimer);
      }

      this.timeoutCheckTimer = setInterval(() => {
        this.checkDeviceTimeout();
      }, 1000); // 每秒检查一次

      console.log('设备超时检测已启动');
    },

    // 停止超时检测
    stopTimeoutCheck() {
      if (this.timeoutCheckTimer) {
        clearInterval(this.timeoutCheckTimer);
        this.timeoutCheckTimer = null;
        console.log('设备超时检测已停止');
      }
    },

    // 检查设备超时
    checkDeviceTimeout() {
      const currentTime = Date.now();

      try {
        // 检查所有设备的超时状态
        this.deviceList.forEach(dock => {
          // 检查机场超时
          if (dock.device_sn && this.deviceLastUpdateTime[dock.device_sn]) {
            const lastUpdate = this.deviceLastUpdateTime[dock.device_sn];
            if (currentTime - lastUpdate > this.DEVICE_TIMEOUT && dock.mode_code !== -1) {
              console.log('机场设备超时，设置为离线:', dock.device_sn);
              this.$set(dock, 'mode_code', -1);
              this.$message.warning(`机场设备超时离线: ${dock.nickname || dock.device_sn}`);
            }
          }

          // 检查无人机超时
          if (dock.children && dock.children.device_sn && this.deviceLastUpdateTime[dock.children.device_sn]) {
            const lastUpdate = this.deviceLastUpdateTime[dock.children.device_sn];
            if (currentTime - lastUpdate > this.DEVICE_TIMEOUT && dock.children.mode_code !== 14) {
              console.log('无人机设备超时，设置为离线:', dock.children.device_sn);
              this.$set(dock.children, 'mode_code', 14);
              this.$message.warning(`无人机设备超时离线: ${dock.children.nickname || dock.children.device_sn}`);
            }
          }
        });
      } catch (error) {
        console.error('检查设备超时失败:', error);
      }
    },

    // 清理WebSocket资源
    cleanupWebSocket() {
      // 清理WebSocket重连回调
      if (this.reconnectCallback) {
        socketService.removeReconnectCallback(this.reconnectCallback);
      }

      // 断开 WebSocket 连接
      socketService.close();

      // 清理超时检测定时器
      this.stopTimeoutCheck();
    },

    // 添加测试OSD数据（用于测试状态图标显示）
    addTestOsdData() {
      // 模拟机场OSD数据，包含无人机状态信息
      const testOsdData = {
        "7CTDM3900BM6WS": {
          sn: "7CTDM3900BM6WS",
          mode_code: 0,
          task_name: "测试任务",
          // 机场经纬度坐标
          longitude: 113.091931,
          latitude: 28.264330,
          // 无人机充电状态
          drone_charge_state: {
            capacity_percent: 85,
            state: 1 // 1表示充电中，0表示空闲
          },
          // GPS/RTK位置状态
          position_state: {
            gps_number: 12,
            rtk_number: 8,
            quality: 5,
            is_calibration: 1,
            is_fixed: 2
          },
          // 网络状态
          network_state: {
            type: 1, // 1表示4G，2表示以太网
            quality: 4, // 0-5的质量等级
            rate: 1024.5 // KB/s
          },
          lastUpdate: Date.now()
        },
        "4TADL330010050": {
          sn: "4TADL330010050",
          mode_code: 0,
          task_name: "测试任务2",
          // 机场经纬度坐标
          longitude: 113.092931,
          latitude: 28.265330,
          // 无人机充电状态（低电量，未充电）
          drone_charge_state: {
            capacity_percent: 15,
            state: 0
          },
          // GPS/RTK位置状态
          position_state: {
            gps_number: 6,
            rtk_number: 3,
            quality: 3,
            is_calibration: 0,
            is_fixed: 1
          },
          // 网络状态（以太网）
          network_state: {
            type: 2,
            quality: 5,
            rate: 2048.0
          },
          lastUpdate: Date.now()
        }
      };

      // 将测试数据设置到osdDock中
      Object.keys(testOsdData).forEach(sn => {
        this.$set(this.osdDock, sn, testOsdData[sn]);
      });

      // 添加测试无人机OSD数据
      const testDroneOsdData = {
        "1581F5BMD232T001EKX6": {
          sn: "1581F5BMD232T001EKX6",
          parent_sn: "4TADL330010050",
          mode_code: 3, // 飞行中
          // 无人机当前经纬度坐标
          longitude: 113.093500,
          latitude: 28.266000,
          battery: {
            // 双电池数据结构
            batteries: [
              {
                firmware_version: "02.00.20.58",
                index: 0,
                loop_times: 76,
                capacity_percent: 95,
                sn: "4BUPM3SDA420LQ",
                sub_type: 0,
                temperature: 30,
                type: 0,
                voltage: 24957,
                high_voltage_storage_days: 100
              },
              {
                firmware_version: "02.00.20.58",
                index: 1,
                loop_times: 76,
                capacity_percent: 94,
                sn: "4BUPM3SDA420LN",
                sub_type: 0,
                temperature: 29.9,
                type: 0,
                voltage: 24927,
                high_voltage_storage_days: 104
              }
            ],
            capacity_percent: 90, // 总电量
            landing_power: 0,
            remain_flight_time: 0,
            return_home_power: 0
          },
          position_state: {
            gps_number: 27,
            rtk_number: 38,
            quality: 5
          },
          wind_direction: 2, // 东北
          wind_speed: 15, // 1.5 m/s (单位：0.1 m/s)
          home_distance: 0.0064091343, // 距离Home点的距离
          height: 42.272186, // 高度
          lastUpdate: Date.now()
        }
      };

      // 将测试无人机数据设置到osdDrone中
      Object.keys(testDroneOsdData).forEach(sn => {
        this.$set(this.osdDrone, sn, testDroneOsdData[sn]);
      });

      console.log('测试OSD数据已添加:', this.osdDock);
      console.log('测试无人机OSD数据已添加:', this.osdDrone);
    }
  }
};
