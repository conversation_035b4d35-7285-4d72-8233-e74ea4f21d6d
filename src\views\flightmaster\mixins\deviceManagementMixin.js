/**
 * 设备管理Mixin
 * 提供设备列表管理、状态判断、视频播放等功能
 */
/* global AMap */
import { getDicts } from '@/api/system/dict/data';
import { deviceInfo } from '@/api/flightmaster/flightmaster';

export default {
  data() {
    return {
      // 存储地图上的设备标记，key为device_sn，value为marker对象
      deviceMarkers: {},
      // 存储设备圆形区域，key为device_sn，value为circle对象
      deviceCircles: {},
      deviceList: [
        {
          "device_sn": "7CTDM3900BM6WS",
          "device_name": "DJI Dock2",
          "workspace_id": "103",
          "control_source": "A",
          "device_desc": "",
          "child_device_sn": "1581F6Q8D242S00C6DVG",
          "domain": 3,
          "type": 2,
          "sub_type": 0,
          "icon_url": {
            "normal_icon_url": "",
            "selected_icon_url": ""
          },
          "latitude":28.181013,
          "longitude":113.07996,
          "status": true,
          "bound_status": true,
          "login_time": "2025-02-23 22:27:47",
          "bound_time": "2025-02-23 22:28:11",
          "nickname": "我的机场",
          "firmware_version": "10.01.1612",
          "workspace_name": "",
          "children": {
            "device_sn": "1581F6Q8D242S00C6DVG",
            "device_name": "M3TD",
            "workspace_id": "103",
            "control_source": "A",
            "device_desc": "",
            "child_device_sn": "",
            "domain": 0,
            "type": 91,
            "sub_type": 1,
            "latitude":28.181213,
          "longitude":113.07996,
            "payloads_list": [
              {
                "payload_sn": "1581F6Q8D242S00C6DVG-0",
                "payload_name": "M3TD Camera",
                "index": 0,
                "control_source": "A",
                "payload_index": "81-0-0"
              }
            ],
            "icon_url": {
              "normal_icon_url": "",
              "selected_icon_url": ""
            },
            "status": false,
            "bound_status": true,
            "login_time": "2025-02-24 13:37:08",
            "bound_time": "2025-02-24 13:36:50",
            "nickname": "松安2号M3TD无人机",
            "firmware_version": "10.01.1612",
            "workspace_name": "",
            "firmware_status": 1,
            "thing_version": "1.3.0",
            "mode_code": 0
          },
          "firmware_status": 1,
          "thing_version": "1.3.0",
          "mode_code": 0,
          "task_name": "暂无任务"
        },
        {
          "device_sn": "4TADL330010050",
          "device_name": "DJI Dock2",
          "workspace_id": "103",
          "control_source": "A",
          "device_desc": "",
          "child_device_sn": "1581F5BMD232T001EKX6",
          "domain": 3,
          "type": 2,
          "sub_type": 0,
          "icon_url": {
            "normal_icon_url": "",
            "selected_icon_url": ""
          },
          "latitude":28.181013,
          "longitude":113.07996,
          "status": true,
          "bound_status": true,
          "login_time": "2025-02-23 22:27:47",
          "bound_time": "2025-02-23 22:28:11",
          "nickname": "测试机场",
          "firmware_version": "10.01.1612",
          "workspace_name": "",
          "children": {
            "device_sn": "1581F5BMD232T001EKX6",
            "device_name": "M3TD",
            "workspace_id": "103",
            "control_source": "A",
            "device_desc": "",
            "child_device_sn": "",
            "domain": 0,
            "type": 91,
            "sub_type": 1,
            "latitude":28.181013,
            "longitude":113.07996,
            "payloads_list": [
              {
                "payload_sn": "1581F5BMD232T001EKX6-0",
                "payload_name": "M3TD Camera",
                "index": 0,
                "control_source": "A",
                "payload_index": "53-0-0"
              }
            ],
            "icon_url": {
              "normal_icon_url": "",
              "selected_icon_url": ""
            },
            "status": true,
            "bound_status": true,
            "login_time": "2025-02-23 22:27:47",
            "bound_time": "2025-02-23 22:28:11",
            "nickname": "测试无人机",
            "firmware_version": "10.01.0504",
            "workspace_name": "",
            "firmware_status": 1,
            "thing_version": "1.3.0",
            "mode_code": 0
          },
          "firmware_status": 1,
          "thing_version": "1.3.0",
          "mode_code": 0,
          "task_name": "暂无任务"
        }

      ],
      // 添加字典数据缓存
      dockStateOptions: [],
      droneStateOptions: [],
      isTextOverflow: false,
      videos: [],
      videoSources: {  // 存储设备对应的视频源
        "7CTDM3900BM6WS": "webrtc://live2.sahy.cloud/live/4TADL330010050-165-0-7",

        "4TADL330010050": "webrtc://live2.sahy.cloud/live/4TADL330010050-165-0-7",
        "1581F5BMD232T001EKX6": "webrtc://live.sahy.cloud/live/1581F5BMD232T001EKX6-53-0-0",


        // 示例：支持多种视频流格式
        "demo_rtsp": "https://highlight-video.cdn.bcebos.com/video/6s/f70a04de-f809-11ef-a6db-7cd30a6157c0.mp4",
        "demo_hls": "https://example.com/stream.m3u8",
        "demo_webrtc": "webrtc://example.com/stream",
        "demo_flv": "https://example.com/stream.flv"
      }
    };
  },

  methods: {
    // 获取机场状态字典数据
    async getDockStateDict() {
      try {
        var response = await getDicts('flight_dock_state')
        this.dockStateOptions = response.data
        response = await getDicts('flight_drone_state')
        this.droneStateOptions = response.data
      } catch (error) {
        // 静默处理错误
      }
    },

    // 获取状态显示文本
    getStateLabel(status, domain) {
      // 处理undefined、null或空值的情况
      if (status === undefined || status === null || status === '') {
        return '离线';
      }

      const statusStr = status.toString();

      if (domain == 3) {
        const dictData = this.dockStateOptions.find(dict => dict.dictValue === statusStr);
        return dictData ? dictData.dictLabel : '离线';
      } else {
        const dictData = this.droneStateOptions.find(dict => dict.dictValue === statusStr);
        return dictData ? dictData.dictLabel : '离线';
      }
    },

    // 获取状态对应的样式类型
    getStateType(modeCode, domain) {
      // 处理undefined、null或空值的情况
      if (modeCode === undefined || modeCode === null || modeCode === '') {
        return 'info'; // 默认返回info类型
      }

      if (domain == 3) {
        const dictData = this.dockStateOptions[modeCode];
        if (dictData) {
          return dictData.listClass;
        }
      } else {
        const dictData = this.droneStateOptions[modeCode];
        if (dictData) {
          return dictData.listClass;
        }
      }

      return 'info'; // 默认返回info类型
    },


    // 检查机场是否在线
    isDockOnline(device) {
      return device && device.mode_code !== -1;
    },

    // 检查无人机是否在线
    isDroneOnline(device) {
      const droneModeCode = device.children.mode_code;
      return droneModeCode !== undefined && droneModeCode !== 14;
    },

    // 创建或更新设备地图标记
    createOrUpdateDeviceMarker(device) {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        return;
      }

      if (!device.longitude || !device.latitude) {
        return;
      }

      const map = this.$refs.amap.map;
      const deviceSn = device.device_sn;

      // 进行坐标系转换（WGS84 -> GCJ02）
      const gcj02Coord = this.wgs84ToGcj02(device.longitude, device.latitude);
      const coordinates = [gcj02Coord.lng, gcj02Coord.lat];

      // 检查是否已存在标记
      if (this.deviceMarkers[deviceSn]) {
        // 更新现有标记位置
        this.deviceMarkers[deviceSn].setPosition(coordinates);

        // 同时更新圆形区域位置
        if (this.deviceCircles[deviceSn]) {
          this.deviceCircles[deviceSn].setCenter(coordinates);
        }
      } else {
        try {
          // 创建新标记
          const marker = new AMap.Marker({
            position: coordinates,
            map: map,
            icon: new AMap.Icon({
              size: new AMap.Size(40, 40),
              image: require('@/assets/images/wrj_jc.png'),
              imageSize: new AMap.Size(40, 40)
            }),
            offset: new AMap.Pixel(-20, -20),
            title: device.nickname || '机场',
            extData: { deviceSn: deviceSn, device: device }
          });

          // 创建圆形区域
          const circle = new AMap.Circle({
            center: coordinates,
            radius: 1000,  // 1000米 = 1公里
            fillColor: 'rgba(173, 216, 230, 0.3)',  // 浅蓝色
            fillOpacity: 0.3,
            strokeColor: '#1E90FF',  // 道奇蓝
            strokeOpacity: 0.8,
            strokeWeight: 2,
            strokeStyle: 'solid',
            zIndex: 50,
          });

          circle.setMap(map);

          // 存储标记和圆形引用
          this.deviceMarkers[deviceSn] = marker;
          this.deviceCircles[deviceSn] = circle;
        } catch (error) {
          // 静默处理错误
        }
      }
    },

    // 移除设备标记
    removeDeviceMarker(deviceSn) {
      if (this.deviceMarkers[deviceSn]) {
        this.deviceMarkers[deviceSn].setMap(null);
        delete this.deviceMarkers[deviceSn];
      }

      if (this.deviceCircles[deviceSn]) {
        this.deviceCircles[deviceSn].setMap(null);
        delete this.deviceCircles[deviceSn];
      }
    },

    // 更新所有设备标记
    updateAllDeviceMarkers() {
      if (!this.deviceList || !Array.isArray(this.deviceList)) {
        return;
      }

      this.deviceList.forEach(device => {
        if (device.domain === 3 && device.longitude && device.latitude) {
          // 只为机场设备创建标记
          this.createOrUpdateDeviceMarker(device);
        }
      });
    },

    // 定位到指定机场
    locateAirport(coord) {
      if (this.$refs.amap && this.$refs.amap.map) {
        // 检查坐标是否有效
        if (coord.longitude && coord.latitude) {
          // 进行坐标系转换（WGS84 -> GCJ02）
          const gcj02Coord = this.wgs84ToGcj02(coord.longitude, coord.latitude);
          // 高德地图需要 [longitude, latitude] 格式的坐标数组
          const coordinates = [gcj02Coord.lng, gcj02Coord.lat];
          this.$refs.amap.map.setZoomAndCenter(15, coordinates);

          // 同时更新该设备的标记位置
          this.createOrUpdateDeviceMarker(coord);
        } else {
          this.$message.warning("该设备暂无有效的位置信息");
        }
      }
    },

    // 修改处理 设备 卡片点击的方法
    handleDeviceClick(device) {
      if (!device) {
        return;
      }
      const videoSrc = this.videoSources[device.device_sn];
      // 检查是否已经存在相同设备的视频
      const existingVideoIndex = this.videos.findIndex(v => v.deviceName === device.nickname);

      if (videoSrc) {
        if (existingVideoIndex === -1) {
          // 如果不存在，则添加新视频
          this.videos.push({
            src: videoSrc,
            deviceName: device.nickname,
            deviceSn: device.device_sn,
            showDebugPanel: false,
            debugLoading: false,
            debugError: null,
            debugInfo: null
          });
        } else {
          // 如果已存在，可以选择聚焦到该视频或者提示用户
          //  添加视频框闪烁 视觉反馈，
          const videoElement = document.querySelectorAll('.video-container')[existingVideoIndex];
          if (videoElement) {
            videoElement.classList.add('highlight');
            setTimeout(() => {
              videoElement.classList.remove('highlight');
            }, 1000);
          }
        }
      }
    },

    // 检查文本是否溢出
    checkTextOverflow(e) {
      const element = e.target;
      this.isTextOverflow = element.scrollWidth > element.offsetWidth;
    },

    //**通过sn获取设备 */
    getDeviceBySn(sn){
      return this.deviceList.find(d => d.device_sn === sn);
    },

    async closeVideo(index) {
      const video = this.videos[index];
      if (!video) return;

      // 如果调试面板是打开的，先关闭调试模式
      if (video.showDebugPanel && video.deviceSn) {
        try {
          await this.closeDebugModeForDevice(video.deviceSn);
        } catch (error) {
          // 即使关闭调试模式失败，也要继续关闭视频
        }
      }

      // 移除视频
      this.videos.splice(index, 1);
    },

    getDeviceInfo() {
      // 使用 Promise 的 then 方法处理异步操作
      deviceInfo().then(response => {
        // 检查响应是否有效
        if (response && response.data) {
          // 将返回的数据赋值给 deviceList
          this.deviceList = null;
          this.deviceList = response.data;

          // 设备信息加载完成后，获取禁飞区数据
          this.$nextTick(() => {
            this.fetchCityNames();
          });
        }
      }).catch(error => {
        // 静默处理错误
      });
    },

    // 获取机场环境信息
    getAirportInfo(deviceSn) {
      return this.osdDock[deviceSn];
    },

    // 格式化温度显示
    formatTemperature(temperature) {
      if (temperature === undefined || temperature === null) return '--';
      return `${temperature.toFixed(1)}°C`;
    },

    // 格式化湿度显示
    formatHumidity(humidity) {
      if (humidity === undefined || humidity === null) return '--';
      return `${humidity.toFixed(1)}%`;
    },

    // 格式化风速显示
    formatWindSpeed(windSpeed) {
      if (windSpeed === undefined || windSpeed === null) return '--';
      return `${windSpeed.toFixed(1)}m/s`;
    },

    // 格式化降雨量显示
    formatRainfall(rainfall) {
      if (rainfall === undefined || rainfall === null) return '--';
      return `${rainfall.toFixed(1)}mm`;
    },

    // 格式化存储容量显示
    formatStorage(storage) {
      if (storage === undefined || storage === null) return '--';
      if (storage.total && storage.used) {
        const usedPercent = ((storage.used / storage.total) * 100).toFixed(1);
        return `${usedPercent}%`;
      }
      return '--';
    }
  }
};
